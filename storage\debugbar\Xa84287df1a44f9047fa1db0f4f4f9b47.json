{"__meta": {"id": "Xa84287df1a44f9047fa1db0f4f4f9b47", "datetime": "2025-09-25 19:49:06", "utime": **********.911938, "method": "GET", "uri": "/event_new", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.439456, "end": **********.911964, "duration": 0.47250795364379883, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.439456, "relative_start": 0, "end": **********.57753, "relative_end": **********.57753, "duration": 0.13807392120361328, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.577543, "relative_start": 0.13808703422546387, "end": **********.911967, "relative_end": 3.0994415283203125e-06, "duration": 0.3344240188598633, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20801952, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "event_new", "param_count": null, "params": [], "start": **********.74842, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/event_new.blade.phpevent_new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fevent_new.blade.php&line=1", "ajax": false, "filename": "event_new.blade.php", "line": "?"}}]}, "route": {"uri": "GET event_new", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\EventNewsController@publicIndex", "namespace": null, "prefix": "", "where": [], "as": "event_new.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FEventNewsController.php&line=179\" onclick=\"\">app/Http/Controllers/EventNewsController.php:179-185</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.019759999999999996, "accumulated_duration_str": "19.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/EventNewsController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\EventNewsController.php", "line": 181}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.649582, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EventNewsController.php:181", "source": "app/Http/Controllers/EventNewsController.php:181", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FEventNewsController.php&line=181", "ajax": false, "filename": "EventNewsController.php", "line": "181"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select count(*) as aggregate from `event_news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventNewsController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\EventNewsController.php", "line": 181}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.662471, "duration": 0.019149999999999997, "duration_str": "19.15ms", "memory": 0, "memory_str": null, "filename": "EventNewsController.php:181", "source": "app/Http/Controllers/EventNewsController.php:181", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FEventNewsController.php&line=181", "ajax": false, "filename": "EventNewsController.php", "line": "181"}, "connection": "shopping67", "start_percent": 0, "width_percent": 96.913}, {"sql": "select count(*) as aggregate from `event_news`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/EventNewsController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\EventNewsController.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7367468, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EventNewsController.php:182", "source": "app/Http/Controllers/EventNewsController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FEventNewsController.php&line=182", "ajax": false, "filename": "EventNewsController.php", "line": "182"}, "connection": "shopping67", "start_percent": 96.913, "width_percent": 3.087}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/event_new\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/event_new", "status_code": "<pre class=sf-dump id=sf-dump-508971649 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-508971649\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2140179136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2140179136\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1052247301 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1052247301\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1027428792 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBWM3FPT29RNXBJYkZEa0Z0UlhYNlE9PSIsInZhbHVlIjoidkxqSDFrRC80cnFnVUNnMEloQ1NvajIydkFTQmZ4d0h4bS93cithYVBta3dEaEV6OTZiTDd3Vm9LZUFTM3NpMTQ3UHBQY3B5b2VQeWlrelFuWXdFcjF4bTVEdTNCR0hjY2tqa0hLaFY5aERXZHFhTW5LYUdwbkh5R0xnS1BOTnciLCJtYWMiOiI0MzJmYmQ4YzRkNzkwNjA3ZmM3M2JkNDU1YjkwZjZjM2ZhZDI1YzUxZWViNDM1MmJkYTU0NGE3NDExMmNjYTE3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5XQU5pWElYWm5PRzBhbDN4bHR5REE9PSIsInZhbHVlIjoiSkUzVEJvaTlRNjMzTHd4WGk4WWRybEQzQ3R3THN6T1Q2ek1sSE5wMTNzWlNjeUFLWUpDUHZZL2hrc3dJNlczanF5VDlwNW50bzdLRndoT2IxRzFvNGtyVGFQdlYyL1JBc3FYQWVHUkpaTnI3YzBhQXk1c0pMV3F3NDQ2YjY4UFQiLCJtYWMiOiI1ZjQyN2IyODQzNmY5ZGU2ZTQ2MDNiNWY5MWM4YTI4ZjcyMzgwNjAwNjllM2EzM2I1ODczN2NkMmFhYTc1MTRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027428792\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1889050884 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889050884\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-132996941 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxSSDQ3TVlNQXRlN3NYRmJ5b1NxZGc9PSIsInZhbHVlIjoiOFowS1dMbDJSY0V4ZnNDNzlhbExNRGdjMCtKa3Y1b2ZweXdHVTFuaHNKVkdKOEIrTzJaMGhKc1dvZDVaSHJQSEN5M3hXVy9ENVFnYlBsTllZd3hTR0tlT0Z3ZS9kSjExcXlsbGF6TCs0ZmpiOGtlYTlYMVlGalo1bFVnaVFoVlEiLCJtYWMiOiIxNTA3NDY3ZmY5MjZmOTk4ZmQ3OTQ1YWNjMzI1ZDJlMjhkMTc4YTlhYWE3NjVjYWM5ZTVmMjk5YzJmNzgxOTJjIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:06 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ik5YZEJsbVVaVFJjRldsd251ZnkyY1E9PSIsInZhbHVlIjoiYXhRVDZvdkNsWmhaelBidzlDdzB3UHV6dWM5L2JjalJlbmZHeUJKb3VTT2RMWlVMcE43ZlZIQU8rRXlINk1xaGhnOWNqSHRmQngxaXVZK0JOMWd6K2Z0eWZlZ08yRTJ3eEJmZmk1enVUR2NrbkpaNnhhMmxJTDNRSjV3Zk92b1AiLCJtYWMiOiJkNGY1NjA5YmU4YTdhZGQ2MDVjODUwOGNkMTNlNTc4Zjk4YThlOTY0ODUwMjM1ZjY5ZTUzOTYwYmQ5ZjdmYjdhIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:06 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxSSDQ3TVlNQXRlN3NYRmJ5b1NxZGc9PSIsInZhbHVlIjoiOFowS1dMbDJSY0V4ZnNDNzlhbExNRGdjMCtKa3Y1b2ZweXdHVTFuaHNKVkdKOEIrTzJaMGhKc1dvZDVaSHJQSEN5M3hXVy9ENVFnYlBsTllZd3hTR0tlT0Z3ZS9kSjExcXlsbGF6TCs0ZmpiOGtlYTlYMVlGalo1bFVnaVFoVlEiLCJtYWMiOiIxNTA3NDY3ZmY5MjZmOTk4ZmQ3OTQ1YWNjMzI1ZDJlMjhkMTc4YTlhYWE3NjVjYWM5ZTVmMjk5YzJmNzgxOTJjIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ik5YZEJsbVVaVFJjRldsd251ZnkyY1E9PSIsInZhbHVlIjoiYXhRVDZvdkNsWmhaelBidzlDdzB3UHV6dWM5L2JjalJlbmZHeUJKb3VTT2RMWlVMcE43ZlZIQU8rRXlINk1xaGhnOWNqSHRmQngxaXVZK0JOMWd6K2Z0eWZlZ08yRTJ3eEJmZmk1enVUR2NrbkpaNnhhMmxJTDNRSjV3Zk92b1AiLCJtYWMiOiJkNGY1NjA5YmU4YTdhZGQ2MDVjODUwOGNkMTNlNTc4Zjk4YThlOTY0ODUwMjM1ZjY5ZTUzOTYwYmQ5ZjdmYjdhIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132996941\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/event_new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}