{"__meta": {"id": "X1dbf729e1bb79cf29506c611b587f1a8", "datetime": "2025-09-25 19:49:11", "utime": **********.453885, "method": "GET", "uri": "/shop", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758804550.902709, "end": **********.453914, "duration": 0.5512049198150635, "duration_str": "551ms", "measures": [{"label": "Booting", "start": 1758804550.902709, "relative_start": 0, "end": **********.060415, "relative_end": **********.060415, "duration": 0.15770602226257324, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060429, "relative_start": 0.15772008895874023, "end": **********.453917, "relative_end": 3.0994415283203125e-06, "duration": 0.39348793029785156, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21190976, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "shop", "param_count": null, "params": [], "start": **********.143378, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.phpshop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=1", "ajax": false, "filename": "shop.blade.php", "line": "?"}}]}, "route": {"uri": "GET shop", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Shopcontroller@shop", "namespace": null, "prefix": "", "where": [], "as": "shop.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=16\" onclick=\"\">app/Http/Controllers/Shopcontroller.php:16-28</a>"}, "queries": {"nb_statements": 27, "nb_failed_statements": 0, "accumulated_duration": 0.0150200***********, "accumulated_duration_str": "15.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 19}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.106142, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:19", "source": "app/Http/Controllers/Shopcontroller.php:19", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=19", "ajax": false, "filename": "Shopcontroller.php", "line": "19"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 19}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.116182, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:19", "source": "app/Http/Controllers/Shopcontroller.php:19", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=19", "ajax": false, "filename": "Shopcontroller.php", "line": "19"}, "connection": "shopping67", "start_percent": 0, "width_percent": 16.112}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 22}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1239831, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:22", "source": "app/Http/Controllers/Shopcontroller.php:22", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=22", "ajax": false, "filename": "Shopcontroller.php", "line": "22"}, "connection": "shopping67", "start_percent": 16.112, "width_percent": 2.929}, {"sql": "select * from `categories` where `categories`.`id` in (1, 2, 3, 4, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.128706, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:22", "source": "app/Http/Controllers/Shopcontroller.php:22", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=22", "ajax": false, "filename": "Shopcontroller.php", "line": "22"}, "connection": "shopping67", "start_percent": 19.041, "width_percent": 2.796}, {"sql": "select * from `brands` where `brands`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.131691, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:22", "source": "app/Http/Controllers/Shopcontroller.php:22", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=22", "ajax": false, "filename": "Shopcontroller.php", "line": "22"}, "connection": "shopping67", "start_percent": 21.838, "width_percent": 2.53}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 25}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.134189, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:25", "source": "app/Http/Controllers/Shopcontroller.php:25", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=25", "ajax": false, "filename": "Shopcontroller.php", "line": "25"}, "connection": "shopping67", "start_percent": 24.368, "width_percent": 1.731}, {"sql": "select * from `products` where `products`.`category_id` = 1 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.368309, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 26.099, "width_percent": 3.395}, {"sql": "select * from `products` where `products`.`category_id` = 2 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.371798, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 29.494, "width_percent": 4.261}, {"sql": "select * from `products` where `products`.`category_id` = 3 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.374815, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 33.755, "width_percent": 3.329}, {"sql": "select * from `products` where `products`.`category_id` = 4 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.377572, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 37.084, "width_percent": 3.662}, {"sql": "select * from `products` where `products`.`category_id` = 5 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.380812, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 40.746, "width_percent": 4.527}, {"sql": "select * from `products` where `products`.`category_id` = 6 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.383927, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 45.273, "width_percent": 3.462}, {"sql": "select * from `products` where `products`.`category_id` = 7 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.387138, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 48.735, "width_percent": 3.862}, {"sql": "select * from `products` where `products`.`category_id` = 8 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.390849, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 52.597, "width_percent": 4.194}, {"sql": "select * from `products` where `products`.`category_id` = 9 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.3949258, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 56.791, "width_percent": 4.993}, {"sql": "select * from `products` where `products`.`category_id` = 10 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.398782, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 61.784, "width_percent": 3.395}, {"sql": "select * from `products` where `products`.`category_id` = 11 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.401554, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 65.18, "width_percent": 2.064}, {"sql": "select * from `products` where `products`.`category_id` = 12 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.404206, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 67.244, "width_percent": 2.929}, {"sql": "select * from `products` where `products`.`category_id` = 13 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.4070432, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 70.173, "width_percent": 2.929}, {"sql": "select * from `products` where `products`.`category_id` = 14 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.4097579, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 73.103, "width_percent": 2.796}, {"sql": "select * from `products` where `products`.`category_id` = 15 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.412831, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 75.899, "width_percent": 3.595}, {"sql": "select * from `products` where `products`.`category_id` = 16 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.4158201, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 79.494, "width_percent": 3.063}, {"sql": "select * from `products` where `products`.`category_id` = 17 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.418454, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 82.557, "width_percent": 2.264}, {"sql": "select * from `products` where `products`.`category_id` = 18 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["18"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.421186, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 84.82, "width_percent": 3.395}, {"sql": "select * from `products` where `products`.`category_id` = 19 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.42455, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 88.216, "width_percent": 3.329}, {"sql": "select * from `products` where `products`.`category_id` = 20 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.427736, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 91.545, "width_percent": 3.529}, {"sql": "select * from `products` where `products`.`category_id` = 21 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.431395, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 95.073, "width_percent": 2.996}, {"sql": "select * from `products` where `products`.`category_id` = 22 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "shop", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/shop.blade.php", "line": 321}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.434273, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "shop:321", "source": "view::shop:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fshop.blade.php&line=321", "ajax": false, "filename": "shop.blade.php", "line": "321"}, "connection": "shopping67", "start_percent": 98.069, "width_percent": 1.931}]}, "models": {"data": {"App\\Models\\Category": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Product": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Brand": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 43, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/shop\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/shop", "status_code": "<pre class=sf-dump id=sf-dump-166882401 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-166882401\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1928137379 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1928137379\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-84023567 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-84023567\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1902570398 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImI3SGFyeWFscGZlYUlFd3c5YlptT2c9PSIsInZhbHVlIjoiVldaSmFjY25sMzVJdWRmd1ZIOWU0M1VKVWFsV0pzY1hMTldoaGIwUjU2bTJsc0VpZjk2WjlaQ20rYjJrMDlpdENYeHdhSEtlbGlsZHBsNG9tVDRxejdSc2J3dmFldEVYSVhjWk1WN3F3V2ZZV3NOREFXOGN1ZXdEWEF0U1ExK3EiLCJtYWMiOiJjZGFhNGJiMThkMmQ0OGJhYTlmMTczNzAzMDMwNjVjNDI5ZjFhNTVhYmQyZTc4NTQ1YzAxN2M4NDMwMWRmN2NhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlV4UG1YNUZMK0UxMXB5Q1ZtWGptbXc9PSIsInZhbHVlIjoibUMwQ09CNm91eHJoZnVBTWl5T3pzZkVlQ1ZpbHhLQnAzcnFFL1N4ZDJmYXE3UGVMbDh2VGRaY05lSFkydVNVM1lvaWlaZ2c2cmNtcS9DVjcxWitMZHlSdjJWdWU3UElQejB2d0NDVXJlRW00Mzk3LzZIMkNCcWZZRGd0Lzh1TVUiLCJtYWMiOiIwNWQxYWQ0YTI4YjExNDkxMGRiZjhhOThkYzRmZTY0YTMzZGMxZTI2MTVmZjI3YTJhOTFjMzI4YWEwM2FlOTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902570398\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-151958526 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151958526\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-185727564 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktjQmEvc3p5TGpJa1M3UmVMOHViRWc9PSIsInZhbHVlIjoiS2s5cjJOTlU0bXA2NXROelBXcEllM3owTFE4Uy9yU25kTjVic2dNM1ozQ2YvZE90ci9Cc0VmVWN6UTI1b3p1MFI4V1B4bEZHc0pwQXZKOHB6cExTMm5CVC9hT0FXU3AxeDlWTU9pRzlzZGFrbU9nMmd4aU5XWjVDREU2ZGRnZGkiLCJtYWMiOiI0NWU4NmFjMmM5NjcwYTU5MDkzNjdhNjA5MTkxMjYyZjJhNjcwMTY4Zjc2NTNhMGM1NDkwMzRkZTQ0Y2Q0MTZhIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImZYSGNCWi81Y08zcEdlVWJwc255VFE9PSIsInZhbHVlIjoidXgvdU1CZzFCdzFoSEVHSUd4RUF1Rithc0xnM1dTZEtWL1V0cDBiTUR2ZmNqQnlxU3Z6aDAvb2g4cW0rOG5TOFIzeWxtZ1pJUWZjcHM5K1B2S3BrdFEyeFdKcHlOeWlsdlJqZ0RNNUcyaFpnMUR1Z2h1eFFrQU8yVXl1eFE2c2EiLCJtYWMiOiJiYmNmOTI0Zjc5NmZmYjI5OWEwZGY0YTBmMDdjMWQ2M2JlYTBhMTllZTFkY2RhZmNhYjA2M2MyNDNhYzA4MzFjIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktjQmEvc3p5TGpJa1M3UmVMOHViRWc9PSIsInZhbHVlIjoiS2s5cjJOTlU0bXA2NXROelBXcEllM3owTFE4Uy9yU25kTjVic2dNM1ozQ2YvZE90ci9Cc0VmVWN6UTI1b3p1MFI4V1B4bEZHc0pwQXZKOHB6cExTMm5CVC9hT0FXU3AxeDlWTU9pRzlzZGFrbU9nMmd4aU5XWjVDREU2ZGRnZGkiLCJtYWMiOiI0NWU4NmFjMmM5NjcwYTU5MDkzNjdhNjA5MTkxMjYyZjJhNjcwMTY4Zjc2NTNhMGM1NDkwMzRkZTQ0Y2Q0MTZhIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImZYSGNCWi81Y08zcEdlVWJwc255VFE9PSIsInZhbHVlIjoidXgvdU1CZzFCdzFoSEVHSUd4RUF1Rithc0xnM1dTZEtWL1V0cDBiTUR2ZmNqQnlxU3Z6aDAvb2g4cW0rOG5TOFIzeWxtZ1pJUWZjcHM5K1B2S3BrdFEyeFdKcHlOeWlsdlJqZ0RNNUcyaFpnMUR1Z2h1eFFrQU8yVXl1eFE2c2EiLCJtYWMiOiJiYmNmOTI0Zjc5NmZmYjI5OWEwZGY0YTBmMDdjMWQ2M2JlYTBhMTllZTFkY2RhZmNhYjA2M2MyNDNhYzA4MzFjIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185727564\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2040639944 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/shop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040639944\", {\"maxDepth\":0})</script>\n"}}