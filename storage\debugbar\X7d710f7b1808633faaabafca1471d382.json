{"__meta": {"id": "X7d710f7b1808633faaabafca1471d382", "datetime": "2025-10-06 09:35:29", "utime": **********.645098, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759718128.925937, "end": **********.645116, "duration": 0.7191791534423828, "duration_str": "719ms", "measures": [{"label": "Booting", "start": 1759718128.925937, "relative_start": 0, "end": **********.04368, "relative_end": **********.04368, "duration": 0.11774301528930664, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.04369, "relative_start": 0.1177530288696289, "end": **********.645118, "relative_end": 1.9073486328125e-06, "duration": 0.6014280319213867, "duration_str": "601ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21974288, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "index", "param_count": null, "params": [], "start": **********.207458, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.phpindex", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\IndexController@index", "namespace": null, "prefix": "", "where": [], "as": "index2", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=22\" onclick=\"\">app/Http/Controllers/IndexController.php:22-56</a>"}, "queries": {"nb_statements": 32, "nb_failed_statements": 0, "accumulated_duration": 0.06568000000000002, "accumulated_duration_str": "65.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 8, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.098652, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "SHOW TABLES LIKE 'reviews'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.098932, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping67", "start_percent": 0, "width_percent": 3.03}, {"sql": "SHOW COLUMNS FROM reviews", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 99}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 87}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.10269, "duration": 0.01175, "duration_str": "11.75ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:99", "source": "app/Http/Controllers/IndexController.php:99", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=99", "ajax": false, "filename": "IndexController.php", "line": "99"}, "connection": "shopping67", "start_percent": 3.03, "width_percent": 17.89}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.124072, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:29", "source": "app/Http/Controllers/IndexController.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=29", "ajax": false, "filename": "IndexController.php", "line": "29"}, "connection": "shopping67", "start_percent": 20.92, "width_percent": 0.487}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1270711, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:30", "source": "app/Http/Controllers/IndexController.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=30", "ajax": false, "filename": "IndexController.php", "line": "30"}, "connection": "shopping67", "start_percent": 21.407, "width_percent": 0.502}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.129292, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:31", "source": "app/Http/Controllers/IndexController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=31", "ajax": false, "filename": "IndexController.php", "line": "31"}, "connection": "shopping67", "start_percent": 21.909, "width_percent": 0.213}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.130846, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping67", "start_percent": 22.122, "width_percent": 0.183}, {"sql": "select * from `products` where `products`.`category_id` in (1, 18, 22, 23, 24, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.165746, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping67", "start_percent": 22.305, "width_percent": 1.035}, {"sql": "select * from `event_news` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.183078, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:36", "source": "app/Http/Controllers/IndexController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=36", "ajax": false, "filename": "IndexController.php", "line": "36"}, "connection": "shopping67", "start_percent": 23.34, "width_percent": 0.837}, {"sql": "select * from `products` order by RAND() limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1854432, "duration": 0.009089999999999999, "duration_str": "9.09ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 24.178, "width_percent": 13.84}, {"sql": "select * from `categories` where `categories`.`id` in (1, 18, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.197221, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 38.018, "width_percent": 0.426}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.19933, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 38.444, "width_percent": 0.228}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 491}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.46019, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 38.672, "width_percent": 0.502}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.56323, "duration": 0.01751, "duration_str": "17.51ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 39.175, "width_percent": 26.66}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 214 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["214", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.5827348, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 65.834, "width_percent": 0.609}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.584732, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 66.443, "width_percent": 3.152}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 257 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["257", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.588591, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 69.595, "width_percent": 0.731}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.590656, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 70.326, "width_percent": 3.73}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 244 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["244", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.594767, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 74.056, "width_percent": 0.502}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5966249, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 74.558, "width_percent": 3.304}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 250 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["250", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.600507, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 77.862, "width_percent": 0.426}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.602358, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 78.289, "width_percent": 3.41}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 239 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["239", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.60655, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 81.699, "width_percent": 0.533}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6084979, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 82.232, "width_percent": 3.258}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 237 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["237", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.612272, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 85.49, "width_percent": 0.426}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.61407, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 85.917, "width_percent": 2.969}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 283 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["283", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.6176789, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 88.886, "width_percent": 0.396}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.619499, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 89.281, "width_percent": 3.197}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 279 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["279", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.6234999, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 92.479, "width_percent": 0.563}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6255279, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 93.042, "width_percent": 3.289}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 241 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["241", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.629355, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 96.331, "width_percent": 0.32}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.631002, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 96.65, "width_percent": 2.984}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 211 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["211", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.634593, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 99.635, "width_percent": 0.365}]}, "models": {"data": {"App\\Models\\Product": {"value": 144, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\EventNews": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FEventNews.php&line=1", "ajax": false, "filename": "EventNews.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 164, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-664760864 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-664760864\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1265155096 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1265155096\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-447375243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-447375243\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-85215933 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/menu-cards</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkIrL2JWRk5McSswS3ZBcXZIWCtZRkE9PSIsInZhbHVlIjoiYVJ3QlVwbjE5K1pHdGx3aHo3cGhZRlFuRkFIdzQ0VjQ4aWg4eFZhNmhvcDd2elN2bzh3SkZjUTU4dkVyM0ZSbmZLZEk5V1J0UWR4Z3EyUnpvU2xFcUZMcU9tK0trZjdpeXFXSnR5ZXJHRXJVbGJybmtORWdjY1MrZ3ZnMTZ5VzFNSFpQelBrUHF1bjAyUWVYOFZtT0F1ZVlEV2ZGalZZMlg3QzBoYW9zZ3VtaHVINjNPWldsaHhMRmxSMVZVTFFxMnViZ21HN3NnQmtTY0hBaHgvVVZPRkdxcnBaa1V2UEJYTTlZaCs0WkNIRT0iLCJtYWMiOiI0YmUzYWQxMTFhNmNmZDhiNzYzZWU3OThiOGEzYzg5OTcwYjY5NTQ1NDk1MDg4NzQ1ZTcyM2RmNzg3OTcyMjNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImV0OUVFcWdYaEw3NkhjalRua2R2V2c9PSIsInZhbHVlIjoibUFzSTAxQWoyWXp5U2gvUUphTTBybmx3THVBRWltcXVhTjYyQkRUOUdUL1lyT09CdytSVGR0RGxNREl5ZHRPOWNyVmZjaWMxSERHamJYSE9KaEJjRklYWENkSVQ4M2ljODJjMi9oV01qb0l3bVJSbkNqeXJSVzE0emE2anNkUWgiLCJtYWMiOiIzOWQ1ZGEwYTkwMTBhMzkzN2U2NWJhMDllNjJlMTQ1YTJmYTg2MzdjYjFiMjRjY2JkZTlmM2JmNTYzYTU5MTk3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IncwN2lBT1dPSFFnVGFjMVNyU0t6UEE9PSIsInZhbHVlIjoiUE5pVU5mUHR4NUx1MGRqWVNwNXZ0VDdaU2xPRCsvT2IxVGw4d0prblpHTE9kZ0xmRURCZCtRUUlmMW8za3NXZC9nM1A1eGpGbjhqOExyVE8zRDlnTGlndGUwaHBTQ1FLT0R3bFV3WjR6TU8rRm1ORFp3Y0J2ektHNlg2TG9WY1giLCJtYWMiOiIyZDRhMzg4OTczY2RmNDlhMzkyMzE1ZTNmMDMwODcwMzgyNDZkMjc5ZWIzNThkODFiNmVlZWU3NDcxN2Q2ZjNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85215933\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2102247779 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|ciypaVlPs0XDfjrsxUZeJB2fnF13npcExqL2nhIjRV0odLHCNXgBMZHt59fU|$2y$10$S2aE4.mXyM2O6ZKRI.5aYO97Lu/xdKeg8EJ14VSNvvDZZ5WlGy8L.</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">38pgvJE1UDpAbpUVDhfLKYbtQu600dmF5Zqu0iOG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102247779\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-865889063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 06 Oct 2025 02:35:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InRabVBqRkZhbnNiKzhXcWRjUzVpcHc9PSIsInZhbHVlIjoibW1KTCtIK0RXQWJiWlhvOFRSV0x0SXFBZlZoU1gwSXYwSEd1ZE1pZzdDT0NoWXZJVjhsclU0OS9LSDJ2cE8vbkZkMnFaMTh6Q1oxb25ab2FUQ1dlUmxCcCs1czV1dkFaaWNvZnhKek5LOWFHaVBkcXR1OSt4bFRPbDczdFMxZzYiLCJtYWMiOiIwNWJlNDcwZjM1ZmNlZmIzMDdiYzhiNWEyMjkzYjk5ZTZhZThmYzg5MTE0MDJhNWFmNDEwYTc4MzVjNTJlZjkyIiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:35:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ii95T1ZxSDdSUzlCV1RFVDlNTG1GTUE9PSIsInZhbHVlIjoicHNWblBXdGdMTTRXaUhmRHR6NkFnWUZKOGozdU5KN3d1YmpQQWVyTXJjTkQzamJuR0ZmUkZ2SUtJOTFSazlsZ2N4WEJzQkI4dEZBOEQ2anJpSnlFbE9oK3RTMGpUMVVrVHlCMUplMFlTRlNybWdYbGp0U3FDU1pzcG9uaGJHOG4iLCJtYWMiOiIxZTgyNzJhYzczOTA4YTM0MGEzNDMzMTI2MDFkMDQyZDc1MjBhZTllYzVhYzAxMmRlNTAzMjdkY2U2MTQyNjhkIiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:35:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InRabVBqRkZhbnNiKzhXcWRjUzVpcHc9PSIsInZhbHVlIjoibW1KTCtIK0RXQWJiWlhvOFRSV0x0SXFBZlZoU1gwSXYwSEd1ZE1pZzdDT0NoWXZJVjhsclU0OS9LSDJ2cE8vbkZkMnFaMTh6Q1oxb25ab2FUQ1dlUmxCcCs1czV1dkFaaWNvZnhKek5LOWFHaVBkcXR1OSt4bFRPbDczdFMxZzYiLCJtYWMiOiIwNWJlNDcwZjM1ZmNlZmIzMDdiYzhiNWEyMjkzYjk5ZTZhZThmYzg5MTE0MDJhNWFmNDEwYTc4MzVjNTJlZjkyIiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:35:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ii95T1ZxSDdSUzlCV1RFVDlNTG1GTUE9PSIsInZhbHVlIjoicHNWblBXdGdMTTRXaUhmRHR6NkFnWUZKOGozdU5KN3d1YmpQQWVyTXJjTkQzamJuR0ZmUkZ2SUtJOTFSazlsZ2N4WEJzQkI4dEZBOEQ2anJpSnlFbE9oK3RTMGpUMVVrVHlCMUplMFlTRlNybWdYbGp0U3FDU1pzcG9uaGJHOG4iLCJtYWMiOiIxZTgyNzJhYzczOTA4YTM0MGEzNDMzMTI2MDFkMDQyZDc1MjBhZTllYzVhYzAxMmRlNTAzMjdkY2U2MTQyNjhkIiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:35:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865889063\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1872310268 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872310268\", {\"maxDepth\":0})</script>\n"}}