<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="ข่าวประชาสัมพันธ์ - BBC HOME KITCHEN ร้านอาหารตามสั่งออนไลน์" />
    <meta name="author" content="BBC HOME KITCHEN" />
    <title>ข่าวประชาสัมพันธ์ - BBC HOME KITCHEN</title>
    <!-- Google Fonts - Prompt -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Bootstrap icons-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link href="<?php echo e(asset('css/styles.css')); ?>" rel="stylesheet" />
    <!-- Restaurant Background CSS -->
    <link href="<?php echo e(asset('css/restaurant-background.css')); ?>" rel="stylesheet" />
    <!-- Performance Optimizations CSS -->
    <link href="<?php echo e(asset('css/performance-optimizations.css')); ?>" rel="stylesheet" />
    <!-- Prompt Font CSS -->
    <link href="<?php echo e(asset('css/prompt-font.css')); ?>" rel="stylesheet" />
    <style>
        /* Global Font Family - Prompt */
        * {
            font-family: 'Prompt', sans-serif !important;
        }

        body {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 400;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .navbar-brand {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        .nav-link {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .btn {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .card-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .section-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        /* Restaurant Theme Styles */

        .navbar {
            background: rgba(255, 255, 255, 0.98) !important;
            border-bottom: 1px solid rgba(255, 107, 107, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #ff6b6b !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
            padding: 8px 16px !important;
            border-radius: 20px;
            transition: all 0.3s ease;
            margin: 0 4px;
        }

        .nav-link:hover {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .nav-link.active {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .navbar-toggler {
            border: 2px solid #ff6b6b;
            color: #ff6b6b;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
        }

        .header-restaurant {
            background: linear-gradient(
                135deg,
                rgba(255, 107, 107, 0.95) 0%,
                rgba(255, 87, 87, 0.9) 30%,
                rgba(255, 154, 0, 0.85) 70%,
                rgba(255, 193, 7, 0.8) 100%
            );
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 32px rgba(255, 107, 107, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .header-restaurant::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('../images/bbc-restaurant-bg.jpg');
            background-size: cover;
            background-position: center;
            opacity: 0.3;
            z-index: -1;
        }

        .section-title {
            color: #2d3436;
            font-weight: 700;
            margin-bottom: 3rem;
            position: relative;
            text-align: center;
            font-size: 2.8rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 2px;
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
        }

        .news-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 250, 250, 0.95)) !important;
            border: 1px solid rgba(255, 107, 107, 0.15);
            border-radius: 16px;
            overflow: hidden;
            box-shadow:
                0 4px 20px rgba(255, 107, 107, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
            pointer-events: auto !important;
            position: relative;
            z-index: 1;
        }

        .news-card:hover {
            background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(255, 248, 248, 0.98)) !important;
            transform: translateY(-5px) scale(1.01);
            box-shadow:
                0 12px 35px rgba(255, 107, 107, 0.18),
                0 4px 15px rgba(255, 87, 87, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 107, 107, 0.25);
        }

        .news-card .card-img-top {
            transition: transform 0.3s ease, filter 0.3s ease;
            height: 200px;
            object-fit: cover;
            border-radius: 16px 16px 0 0;
            position: relative;
        }

        .news-card:hover .card-img-top {
            transform: scale(1.05);
            filter: brightness(1.1) contrast(1.05);
        }

        .news-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(
                180deg,
                rgba(255, 107, 107, 0.1) 0%,
                transparent 50%,
                rgba(0, 0, 0, 0.05) 100%
            );
            border-radius: 16px 16px 0 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .news-card:hover::before {
            opacity: 1;
        }

        .news-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow:
                0 2px 8px rgba(255, 107, 107, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 2;
            backdrop-filter: blur(10px);
        }

        .btn-read-more {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 50%, #ee5a24 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 28px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            box-shadow:
                0 4px 15px rgba(255, 107, 107, 0.3),
                0 2px 5px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            pointer-events: auto !important;
            position: relative;
            z-index: 10;
            overflow: hidden;
        }

        .btn-read-more:hover {
            background: linear-gradient(135deg, #ff5252 0%, #ff4444 50%, #e74c3c 100%);
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 6px 20px rgba(255, 107, 107, 0.4),
                0 3px 8px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-read-more::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-read-more:hover::before {
            left: 100%;
        }

        /* Modal Enhancement */
        .modal-content {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 250, 250, 0.95)) !important;
            color: #333 !important;
            border: 1px solid rgba(255, 107, 107, 0.1);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 5px 15px rgba(255, 107, 107, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .modal-body {
            background: transparent !important;
            color: #333 !important;
        }

        .description-content p {
            color: #333 !important;
            font-size: 16px !important;
            line-height: 1.8 !important;
            font-weight: 400 !important;
            text-align: left !important;
        }

        .news-meta {
            background: #f8f9fa !important;
            border: 1px solid #e9ecef !important;
        }

        .news-meta strong {
            color: #333 !important;
        }

        .news-meta span {
            color: #666 !important;
        }

        /* Card Body Enhancement */
        .card-body {
            padding: 1.5rem;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
            position: relative;
        }

        .card-title {
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 1rem;
            line-height: 1.4;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .card-text {
            color: #636e72;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        /* Card Footer Enhancement */
        .card-footer {
            background: linear-gradient(145deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
            border-top: 1px solid rgba(255, 107, 107, 0.1);
            padding: 1rem 1.5rem;
        }

        /* Fix Click Issues */
        .card, .news-card, .btn, button, a {
            pointer-events: auto !important;
            position: relative;
            z-index: 1;
        }

        .card-footer, .card-body {
            pointer-events: auto !important;
            position: relative;
            z-index: 2;
        }

        /* Remove any blocking overlays */
        *::before, *::after {
            pointer-events: none !important;
        }

        .header-restaurant::before {
            pointer-events: none !important;
            z-index: -1 !important;
        }

        /* Beautiful Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        /* Loading Shimmer Effect */
        .loading-shimmer {
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0.1) 25%,
                rgba(255, 255, 255, 0.3) 50%,
                rgba(255, 255, 255, 0.1) 75%
            );
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        /* Modal Backdrop Fix */
        .modal-backdrop {
            display: none !important;
        }

        .modal-backdrop.show {
            display: none !important;
        }

        .modal-backdrop.fade {
            display: none !important;
        }

        /* Ensure modal works without backdrop */
        .modal {
            background: rgba(0, 0, 0, 0.5) !important;
        }

        .modal.show {
            background: rgba(0, 0, 0, 0.5) !important;
        }

        /* Prevent body scroll issues */
        body.modal-open {
            overflow: auto !important;
            padding-right: 0 !important;
        }

        .footer-restaurant {
            background: linear-gradient(
                135deg,
                rgba(52, 58, 64, 0.95) 0%,
                rgba(73, 80, 87, 0.9) 100%
            ) !important;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Section Backgrounds */
        section {
            position: relative;
        }

        section.py-5 {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.97), rgba(248, 249, 250, 0.95));
            margin: 20px 0;
            border-radius: 20px;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.05),
                0 1px 3px rgba(0, 0, 0, 0.03),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        /* Text Enhancements */
        .text-white {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-title {
            background: linear-gradient(135deg, #ff6b6b, #ff9a00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            text-shadow: none;
        }

        .news-meta {
            color: #636e72;
            font-size: 0.9rem;
        }

        .news-meta i {
            color: #ff6b6b;
        }

        /* Pagination Styles */
        .pagination .page-link {
            border-radius: 25px;
            margin: 0 2px;
            border: none;
            color: #ff6b6b;
            font-weight: 500;
        }

        .pagination .page-link:hover {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            transform: translateY(-2px);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
        }

        /* Empty state styles */
        .empty-state {
            padding: 3rem;
            text-align: center;
        }

        .empty-state .fs-1 {
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Navigation-->
    <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container px-4 px-lg-5">
                <a class="navbar-brand" href="<?php echo e(route('index2')); ?>">🍽️ BBC HOME KITCHEN</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0 ms-lg-4">
                        <li class="nav-item"><a class="nav-link" href="<?php echo e(route('index2')); ?>">หน้าแรก</a></li>
                        <li class="nav-item"><a class="nav-link active" aria-current="page" href="/event_new">ข่าวประชาสัมพันธ์</a></li>
                        <li class="nav-item"><a class="nav-link" href="<?php echo e(route('reviews.index')); ?>">รีวิว</a></li>
                        <li class="nav-item"><a class="nav-link" href="<?php echo e(route('shop.index')); ?>">เมนูอาหาร</a></li>                                                                      
                        <li class="nav-item"><a class="nav-link" href="/about">เกี่ยวกับเรา</a></li>
                    </ul>

                    <!-- Login/Register buttons on the right -->
                    <div class="d-flex">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo e(Auth::user()->name); ?>

                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('customer.profile')); ?>">
                                    <i class="bi bi-person me-2"></i>โปรไฟล์ของฉัน
                                </a></li>
                                <?php if(Auth::user()->isAdmin()): ?>
                                    <li><a class="dropdown-item" href="<?php echo e(route('dashboard')); ?>">
                                        <i class="bi bi-speedometer2 me-2"></i>แดชบอร์ด
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-item">
                                            <i class="bi bi-box-arrow-right me-2"></i>ออกจากระบบ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-light me-2">
                            <i class="bi bi-box-arrow-in-right me-1"></i>เข้าสู่ระบบ
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="btn btn-light">
                            <i class="bi bi-person-plus me-1"></i>สมัครสมาชิก
                        </a>
                    <?php endif; ?>
                </div>
                    
                   
                        
                </div>
            </div>
                        
        </nav>
    <!-- Header-->
    <header class="header-restaurant py-5 text-white text-center">
        <div class="container px-4 px-lg-5">
            <h1 class="display-4 fw-bolder">📰 ข่าวประชาสัมพันธ์</h1>
            <p class="lead fw-normal text-white-50">ติดตามข่าวสารและโปรโมชั่นล่าสุดจาก BBC HOME KITCHEN</p>
            <p class="text-white-50">มีข่าวทั้งหมด <?php echo e($countEvent); ?> รายการ | อัปเดตเมนูใหม่ ข้อเสนอพิเศษ และกิจกรรมต่างๆ</p>
        </div>
    </header>

    <!-- Section-->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="text-center mb-5">
                <h2 class="section-title">📢 ข่าวสารและกิจกรรม</h2>
                <p class="lead fw-normal text-muted mb-0">อัปเดตข่าวสารล่าสุดจากร้านอาหารของเรา</p>
            </div>
            <div class="row gx-4 gx-lg-5 row-cols-1 row-cols-md-2 row-cols-xl-3 justify-content-center">
                <?php $__empty_1 = true; $__currentLoopData = $eventNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col mb-5">
                        <div class="card news-card h-100">
                            <!-- News badge -->
                            <div class="news-badge position-absolute" style="top: 1rem; left: 1rem; z-index: 10;">
                                📰 ข่าวใหม่
                            </div>

                            <!-- Event image -->
                            <?php if($event->pic): ?>
                                <img class="card-img-top" src="<?php echo e(asset('storage/event_news/' . $event->pic)); ?>" alt="<?php echo e($event->title); ?>" />
                            <?php else: ?>
                                <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 200px; background: linear-gradient(135deg, #ffeaa7, #fdcb6e); font-size: 4rem;">
                                    📰
                                </div>
                            <?php endif; ?>

                            <!-- Event details -->
                            <div class="card-body p-4">
                                <div class="text-center">
                                    <!-- Event title -->
                                    <h5 class="fw-bolder text-dark mb-3"><?php echo e($event->title); ?></h5>
                                    <!-- Event description -->
                                    <p class="text-muted mb-3"><?php echo e(Str::limit($event->description, 100)); ?></p>

                                    <!-- Event meta info -->
                                    <div class="news-meta mb-2">
                                        <div class="d-flex justify-content-center align-items-center mb-2">
                                            <i class="bi bi-calendar-event me-2"></i>
                                            <span><?php echo e(\Carbon\Carbon::parse($event->event_date)->format('d/m/Y')); ?></span>
                                        </div>
                                        <div class="d-flex justify-content-center align-items-center">
                                            <i class="bi bi-clock me-2"></i>
                                            <span>เผยแพร่: <?php echo e(\Carbon\Carbon::parse($event->created_at)->format('d/m/Y')); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Event actions -->
                            <div class="card-footer p-4 pt-0 border-top-0 bg-transparent">
                                <div class="text-center">
                                    <button class="btn btn-read-more" data-bs-toggle="modal" data-bs-target="#eventModal<?php echo e($event->id); ?>">
                                        <i class="bi bi-book-open me-2"></i>อ่านเพิ่มเติม
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal for event details -->
                    <div class="modal fade" id="eventModal<?php echo e($event->id); ?>" tabindex="-1" aria-labelledby="eventModalLabel<?php echo e($event->id); ?>" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="border-radius: 15px; border: none; background: white;">
                                <div class="modal-header" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; border-radius: 15px 15px 0 0;">
                                    <h5 class="modal-title fw-bold" id="eventModalLabel<?php echo e($event->id); ?>">📰 <?php echo e($event->title); ?></h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body p-4" style="background: white; color: #333;">
                                    <?php if($event->pic): ?>
                                        <img src="<?php echo e(asset('storage/event_news/' . $event->pic)); ?>" alt="<?php echo e($event->title); ?>" class="img-fluid mb-4 rounded" style="border-radius: 10px; max-height: 300px; width: 100%; object-fit: cover;">
                                    <?php endif; ?>

                                    <div class="news-meta mb-4" style="background: #f8f9fa; padding: 15px; border-radius: 10px; border: 1px solid #e9ecef;">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="bi bi-calendar-event me-2 text-primary"></i>
                                            <strong style="color: #333;">วันที่จัดกิจกรรม:</strong>
                                            <span class="ms-2" style="color: #666;"><?php echo e(\Carbon\Carbon::parse($event->event_date)->format('d F Y')); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-clock me-2 text-success"></i>
                                            <strong style="color: #333;">เผยแพร่เมื่อ:</strong>
                                            <span class="ms-2" style="color: #666;"><?php echo e(\Carbon\Carbon::parse($event->created_at)->format('d F Y H:i')); ?> น.</span>
                                        </div>
                                    </div>

                                    <div class="content-section">
                                        <h6 class="fw-bold mb-3" style="color: #ff6b6b; border-bottom: 2px solid #ff6b6b; padding-bottom: 8px;">
                                            📝 รายละเอียดข่าว
                                        </h6>
                                        <div class="description-content" style="background: #fff; padding: 20px; border-radius: 10px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                            <p style="line-height: 1.8; color: #333; font-size: 16px; margin: 0; white-space: pre-wrap; font-weight: 400;"><?php echo e($event->description); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer" style="background: #f8f9fa; border-radius: 0 0 15px 15px; border-top: 1px solid #e9ecef;">
                                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border-radius: 25px; padding: 10px 25px; color: #666; border-color: #ddd;">
                                        <i class="bi bi-x-circle me-2"></i>ปิด
                                    </button>
                                 <!--   <button type="button" class="btn btn-primary" style="border-radius: 25px; padding: 10px 25px; background: linear-gradient(135deg, #ff6b6b, #ee5a24); border: none;">
                                        <i class="bi bi-share me-2"></i>แชร์ข่าว
                                    </button> -->
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <div class="card news-card p-5">
                            <div class="fs-1 mb-3">📰</div>
                            <h4 class="text-muted">ยังไม่มีข่าวประชาสัมพันธ์</h4>
                            <p class="text-muted">กรุณาติดตามข่าวสารและโปรโมชั่นจากเราในภายหลัง</p>
                            <a href="<?php echo e(route('shop.index')); ?>" class="btn btn-read-more">
                                <i class="bi bi-arrow-left me-2"></i>กลับไปดูเมนูอาหาร
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if($eventNews->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($eventNews->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer-->
    <footer class="py-4 footer-restaurant text-white text-center">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-white mb-3">🍽️ BBC HOME KITCHEN</h5>
                    <p class="text-white-50">ร้านอาหารตามสั่งออนไลน์ อาหารอร่อยส่งถึงบ้าน</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-white-50 mb-0">&copy; 2024 BBC HOME KITCHEN</p>
                    <p class="text-white-50">ติดตามข่าวสารและโปรโมชั่นจากเรา</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap core JS-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Core theme JS-->
    <script src="<?php echo e(asset('js/scripts.js')); ?>"></script>

    <!-- Custom JS for News Page -->
    <script>
        // Fix modal backdrop issues
        document.addEventListener('DOMContentLoaded', function() {
            // Remove any existing backdrops on page load
            const existingBackdrops = document.querySelectorAll('.modal-backdrop');
            existingBackdrops.forEach(backdrop => backdrop.remove());

            // Clean body classes
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });

        // Enhanced card interactions with beautiful animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.news-card');

            // Beautiful staggered fade-in animation
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.classList.add('loading-shimmer');

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.classList.remove('loading-shimmer');
                    card.classList.add('fade-in-up');
                }, index * 150);
            });

            // Enhanced hover effects with smooth transitions
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.01)';
                    this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add parallax effect to title
            const title = document.querySelector('.section-title');
            if (title) {
                window.addEventListener('scroll', () => {
                    const scrolled = window.pageYOffset;
                    const rate = scrolled * -0.5;
                    title.style.transform = `translateY(${rate}px)`;
                });
            }
        });

        // Simplified modal handling
        document.addEventListener('DOMContentLoaded', function() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.addEventListener('show.bs.modal', function() {
                    // Remove any existing backdrops
                    const existingBackdrops = document.querySelectorAll('.modal-backdrop');
                    existingBackdrops.forEach(backdrop => backdrop.remove());
                });

                modal.addEventListener('hidden.bs.modal', function() {
                    // Clean up any remaining backdrops
                    const remainingBackdrops = document.querySelectorAll('.modal-backdrop');
                    remainingBackdrops.forEach(backdrop => backdrop.remove());

                    // Reset body styles
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                });

                modal.addEventListener('hide.bs.modal', function() {
                    // Start cleanup process
                    setTimeout(() => {
                        const backdrops = document.querySelectorAll('.modal-backdrop');
                        backdrops.forEach(backdrop => backdrop.remove());
                    }, 50);
                });
            });
        });

        // Reduced backdrop cleanup frequency
        setInterval(function() {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 0) {
                backdrops.forEach(backdrop => backdrop.remove());
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        }, 3000);

        // Handle page visibility change
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        });
    </script>

    <!-- Restaurant Effects JS-->
    <script src="<?php echo e(asset('js/restaurant-effects.js')); ?>"></script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\shopping67\resources\views/event_new.blade.php ENDPATH**/ ?>