{"__meta": {"id": "X48b5ce98db070fd46bce562449227865", "datetime": "2025-09-25 20:03:35", "utime": **********.601947, "method": "GET", "uri": "/reviews/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.217407, "end": **********.602, "duration": 0.38459300994873047, "duration_str": "385ms", "measures": [{"label": "Booting", "start": **********.217407, "relative_start": 0, "end": **********.396954, "relative_end": **********.396954, "duration": 0.17954707145690918, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.396969, "relative_start": 0.17956209182739258, "end": **********.602004, "relative_end": 4.0531158447265625e-06, "duration": 0.20503497123718262, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21031704, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "reviews.show", "param_count": null, "params": [], "start": **********.473637, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/reviews/show.blade.phpreviews.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Freviews%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}]}, "route": {"uri": "GET reviews/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ReviewController@show", "namespace": null, "prefix": "", "where": [], "as": "reviews.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=88\" onclick=\"\">app/Http/Controllers/ReviewController.php:88-92</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00497********99999, "accumulated_duration_str": "4.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 90}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.433585, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReviewController.php:90", "source": "app/Http/Controllers/ReviewController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=90", "ajax": false, "filename": "ReviewController.php", "line": "90"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `reviews` where `reviews`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.446566, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "ReviewController.php:90", "source": "app/Http/Controllers/ReviewController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=90", "ajax": false, "filename": "ReviewController.php", "line": "90"}, "connection": "shopping67", "start_percent": 0, "width_percent": 58.635}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.456857, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ReviewController.php:90", "source": "app/Http/Controllers/ReviewController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=90", "ajax": false, "filename": "ReviewController.php", "line": "90"}, "connection": "shopping67", "start_percent": 58.635, "width_percent": 14.659}, {"sql": "select * from `products` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.460986, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ReviewController.php:90", "source": "app/Http/Controllers/ReviewController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=90", "ajax": false, "filename": "ReviewController.php", "line": "90"}, "connection": "shopping67", "start_percent": 73.293, "width_percent": 7.831}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "reviews.show", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/reviews/show.blade.php", "line": 154}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.5841749, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 81.124, "width_percent": 18.876}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Review": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FReview.php&line=1", "ajax": false, "filename": "Review.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/reviews/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/reviews/1", "status_code": "<pre class=sf-dump id=sf-dump-1932903754 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1932903754\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1034270485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1034270485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-318356937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-318356937\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1707924580 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjZsWHZSU0dCOXlValcvVkx2U0s3V1E9PSIsInZhbHVlIjoiZ25wUmpYMDNQdVRyTTBRaFFxTlEvV0xTZGxqcUhsUmJCYlNiUFZnNUowSHVpUWVSZ0lwQWtKTkN5UkZyMUFoeERxdmJ3WnFGUjlxZTduZlNqNkEwbXBQaWNnOEhqSkZQUXVOaTNZNW5tN2t3TG5vQXFkZlFyMFVDT3ZaUDY3b0kiLCJtYWMiOiJjNTJlMDQzYmQ3MjgyNGY3NTcyZmU5YzNhZjllOTg1NzVjYWEwZDdjYjFlZWM3MjM2YTBjOGY4MDJmMTcxYjJmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilc0d2lROWRtK3B1d2ZYdHpMSExsTWc9PSIsInZhbHVlIjoiMm5IamhUWFRmMlRPb3VvR3l6ZUg5Rjloc3htSTlyYi9OV1JTa1NFWEZxdlJhSndSZ3VsZnhlalhwb3had1RuL3FBa1BLbnhtUHhmZ2Y2UnEwQXpZSXowQkMxYnA0USs2MzIxQTRXZjBIcWhWc29Cd2lvVmhGUnVJYVdURnFxdk8iLCJtYWMiOiJhYzg3MTY2Mzg2OGQzOTAzMzJjN2Y0MDYyNTJhYjM5YjFhNWE5NjY2YjIyMzVjMGM4Y2FiYzE5YmVmMDlkYWNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707924580\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-731790497 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WMTIWfNahNQIltL7Sh8nxhpUhpWn05qNcCODNhVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731790497\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1284987231 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 13:03:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imd1RkxPV2QyNGxpQldwTWdNaFgwMVE9PSIsInZhbHVlIjoiT3dFNmlLbjFNUnFIMzMrWmszRks0eG9QOTZoV1IzYTlNQUFlODBRMDNxYzBiUmYzdkU5d01DUlNXQThBMGlLZmxFeEZ0ZUhZdDB6WG5XYVJRM1hNVWo5VHRXTU1VVWFTYUwwT2FKN2p4dW5DVWxzTGVCMGw4UUk0M0Zhd2NmY2ciLCJtYWMiOiJjOTMyYTEzMDM3NGJjMDllZjdlOTE4NDNlZTExZGI1MGY4ZmIyZDliNmFjNTI2Zjk5ZGJlMjgxZWYxNGNkMzUxIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:03:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImkrSDg3d2tZcmpHRDl2VHlKQ0ZmN1E9PSIsInZhbHVlIjoiRy9aVFR0QjRvVk1mblFIWDZnenVsaDZERC9TU20rMHBydllvUERBMWlZMlIwQ3BZMktZM25jeHdhSDRSR3NJdEFKalJubWlaeXV6RS9GcERkRWJYQUZHNGp0NXpiS282Qm15ZGFXbEJRd05WVTh2WWUzWkxoTFBVSmhnUStjWUoiLCJtYWMiOiJkMTc2OTdjNjNlZmExNjA3YjM4M2EwODMzYzg0OGI3MDFjMzZlNTA4Y2UwMTYyZDVhYjU2MjRhOWRjNDczNTQxIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:03:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imd1RkxPV2QyNGxpQldwTWdNaFgwMVE9PSIsInZhbHVlIjoiT3dFNmlLbjFNUnFIMzMrWmszRks0eG9QOTZoV1IzYTlNQUFlODBRMDNxYzBiUmYzdkU5d01DUlNXQThBMGlLZmxFeEZ0ZUhZdDB6WG5XYVJRM1hNVWo5VHRXTU1VVWFTYUwwT2FKN2p4dW5DVWxzTGVCMGw4UUk0M0Zhd2NmY2ciLCJtYWMiOiJjOTMyYTEzMDM3NGJjMDllZjdlOTE4NDNlZTExZGI1MGY4ZmIyZDliNmFjNTI2Zjk5ZGJlMjgxZWYxNGNkMzUxIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:03:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImkrSDg3d2tZcmpHRDl2VHlKQ0ZmN1E9PSIsInZhbHVlIjoiRy9aVFR0QjRvVk1mblFIWDZnenVsaDZERC9TU20rMHBydllvUERBMWlZMlIwQ3BZMktZM25jeHdhSDRSR3NJdEFKalJubWlaeXV6RS9GcERkRWJYQUZHNGp0NXpiS282Qm15ZGFXbEJRd05WVTh2WWUzWkxoTFBVSmhnUStjWUoiLCJtYWMiOiJkMTc2OTdjNjNlZmExNjA3YjM4M2EwODMzYzg0OGI3MDFjMzZlNTA4Y2UwMTYyZDVhYjU2MjRhOWRjNDczNTQxIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:03:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284987231\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-283134116 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/reviews/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283134116\", {\"maxDepth\":0})</script>\n"}}