{"__meta": {"id": "Xadda1dc7afb40310213d6ee9a68c93d9", "datetime": "2025-10-06 09:37:21", "utime": **********.812947, "method": "GET", "uri": "/about", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.565649, "end": **********.812984, "duration": 0.24733495712280273, "duration_str": "247ms", "measures": [{"label": "Booting", "start": **********.565649, "relative_start": 0, "end": **********.66196, "relative_end": **********.66196, "duration": 0.09631085395812988, "duration_str": "96.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.661969, "relative_start": 0.09631991386413574, "end": **********.812986, "relative_end": 1.9073486328125e-06, "duration": 0.1510169506072998, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20794352, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "about", "param_count": null, "params": [], "start": **********.714493, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/about.blade.phpabout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fabout.blade.php&line=1", "ajax": false, "filename": "about.blade.php", "line": "?"}}]}, "route": {"uri": "GET about", "middleware": "web", "uses": "Closure() {#240\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#235 …}\n  file: \"C:\\xampp\\htdocs\\shopping67\\routes\\web.php\"\n  line: \"35 to 37\"\n}", "namespace": null, "prefix": "", "where": [], "as": "about55", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Froutes%2Fweb.php&line=35\" onclick=\"\">routes/web.php:35-37</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02585, "accumulated_duration_str": "25.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "view", "name": "about", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/about.blade.php", "line": 468}], "start": **********.767445, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "about", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/about.blade.php", "line": 468}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.775503, "duration": 0.02585, "duration_str": "25.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/about\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/about", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2107452432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2107452432\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1382196519 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1382196519\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2111344855 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/shop/product/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkIrL2JWRk5McSswS3ZBcXZIWCtZRkE9PSIsInZhbHVlIjoiYVJ3QlVwbjE5K1pHdGx3aHo3cGhZRlFuRkFIdzQ0VjQ4aWg4eFZhNmhvcDd2elN2bzh3SkZjUTU4dkVyM0ZSbmZLZEk5V1J0UWR4Z3EyUnpvU2xFcUZMcU9tK0trZjdpeXFXSnR5ZXJHRXJVbGJybmtORWdjY1MrZ3ZnMTZ5VzFNSFpQelBrUHF1bjAyUWVYOFZtT0F1ZVlEV2ZGalZZMlg3QzBoYW9zZ3VtaHVINjNPWldsaHhMRmxSMVZVTFFxMnViZ21HN3NnQmtTY0hBaHgvVVZPRkdxcnBaa1V2UEJYTTlZaCs0WkNIRT0iLCJtYWMiOiI0YmUzYWQxMTFhNmNmZDhiNzYzZWU3OThiOGEzYzg5OTcwYjY5NTQ1NDk1MDg4NzQ1ZTcyM2RmNzg3OTcyMjNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlZqY21ybnhnemxRVmFjNXd5ajkwbXc9PSIsInZhbHVlIjoid2ZRNzZ2QmQwRllBdHFyTUhNYUZpYkhYS3N6Tks3eWE0cWZYTlBxLzZ3dEE2WTRXVHg0SEFmOS9WRE5Sa1hiZlVnckcvMU1QUHRnZ3RkY3dvWUJ0dHA2K0VOZlFrVHhRZVRrNDlPbFRYRnRrc0hpZ0o3dENDZHJ4ekY5bGNSdEsiLCJtYWMiOiI1OGNiZTI5NDkxOWU4NzcwM2IwMTFmYTM1NmM3MDlhMjc5YmVhNzI1MzE0NjI2ODUwNmEyOTE2YWQ0M2E0NDY5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZOVWxONWtJTG1hMGYxZG1CMHZPL3c9PSIsInZhbHVlIjoiOVkrVmZQUzNRU0NjN2NJZ3lyRnhrM1hwSkpLc3hhK1dUWHVKbURHekQwOVEzQ0tGa2oxbDZFeFczWEVXU3c5U1U4bkErdmFoU0pud2p1RHRzZnRaVExmcXlMSXZrNkdvd3dxNDZOLzUwQnFlL044K1RWaEFLOXUyQ3dNMHpXcloiLCJtYWMiOiI2NmNhNWQ0MGNjODJhZDFjMzdlZWE2ZmJkNWI3M2EyNDdkNjBlYTQ1MWM0ZTdkNzcwMzEzNmViNmE5MDg1ZmRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111344855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1517720131 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|ciypaVlPs0XDfjrsxUZeJB2fnF13npcExqL2nhIjRV0odLHCNXgBMZHt59fU|$2y$10$S2aE4.mXyM2O6ZKRI.5aYO97Lu/xdKeg8EJ14VSNvvDZZ5WlGy8L.</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">38pgvJE1UDpAbpUVDhfLKYbtQu600dmF5Zqu0iOG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517720131\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-918812563 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 06 Oct 2025 02:37:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBkZWhTMGNhU0cycVRaQTZkd054R0E9PSIsInZhbHVlIjoiZGRaOUtYMjRFOTlKOXpsM3g3R1M4cG4rRGRxQi91Mmd0NWJSSXlJNHBkbEJZb2VXbTl5QWlzM2ZyZDBzUVp2Z3V1dThZZXNDL2lVOHRiOHMwQWIzcVBUcldjRS9lR283SGxacE9PVTNuZmxHZFF6Y0RtdHA3WVZlNG0zSnZQUTAiLCJtYWMiOiI2ZTUwMTc2MDc3ZTEyMWU1YzFmOWYyODRkZmY0OGQ1MWFiZDY0MjljMzA5MjIzZDc0OTU2NDEzNjY2ZjdkZjYxIiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:37:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkhPUkhnS3h5ZmpTYWlZVnpjRVZublE9PSIsInZhbHVlIjoiWE1nUnBTR0NkZGNmWTh1NUxuWGgxRS9LVUxKRHlKS1paeE5ZeWZUT0dsUWlOL3FwcUlCYllUbEwrMWJONTF5akl4d281UitPMDYybklPclB6ZnpmVlE5bHZTVHJIZlR3MDVVY2hic3ExZmU0RDNFL041cEdOTWFFazRTQmhJV3giLCJtYWMiOiIyNjkzNTkyY2U5ZWZjMTczZjJjNDc1MzAwMTQ5MDMxMjVjOTU5OTRjODk3MWRhZjM2ZTJmYWVjMWU1NGFkOTI5IiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:37:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBkZWhTMGNhU0cycVRaQTZkd054R0E9PSIsInZhbHVlIjoiZGRaOUtYMjRFOTlKOXpsM3g3R1M4cG4rRGRxQi91Mmd0NWJSSXlJNHBkbEJZb2VXbTl5QWlzM2ZyZDBzUVp2Z3V1dThZZXNDL2lVOHRiOHMwQWIzcVBUcldjRS9lR283SGxacE9PVTNuZmxHZFF6Y0RtdHA3WVZlNG0zSnZQUTAiLCJtYWMiOiI2ZTUwMTc2MDc3ZTEyMWU1YzFmOWYyODRkZmY0OGQ1MWFiZDY0MjljMzA5MjIzZDc0OTU2NDEzNjY2ZjdkZjYxIiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:37:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkhPUkhnS3h5ZmpTYWlZVnpjRVZublE9PSIsInZhbHVlIjoiWE1nUnBTR0NkZGNmWTh1NUxuWGgxRS9LVUxKRHlKS1paeE5ZeWZUT0dsUWlOL3FwcUlCYllUbEwrMWJONTF5akl4d281UitPMDYybklPclB6ZnpmVlE5bHZTVHJIZlR3MDVVY2hic3ExZmU0RDNFL041cEdOTWFFazRTQmhJV3giLCJtYWMiOiIyNjkzNTkyY2U5ZWZjMTczZjJjNDc1MzAwMTQ5MDMxMjVjOTU5OTRjODk3MWRhZjM2ZTJmYWVjMWU1NGFkOTI5IiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:37:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918812563\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1698186211 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/about</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698186211\", {\"maxDepth\":0})</script>\n"}}