{"__meta": {"id": "X54c5949a705b6324529726510ac98722", "datetime": "2025-09-25 19:49:09", "utime": 1758804549.257375, "method": "GET", "uri": "/reviews", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.197631, "end": 1758804549.257406, "duration": 1.0597751140594482, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": **********.197631, "relative_start": 0, "end": **********.338302, "relative_end": **********.338302, "duration": 0.1406710147857666, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.338316, "relative_start": 0.1406850814819336, "end": 1758804549.257408, "relative_end": 1.9073486328125e-06, "duration": 0.9190919399261475, "duration_str": "919ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21146608, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "reviews.index", "param_count": null, "params": [], "start": **********.829084, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/reviews/index.blade.phpreviews.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Freviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": 1758804549.01428, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "layouts.restaurant", "param_count": null, "params": [], "start": 1758804549.117162, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/layouts/restaurant.blade.phplayouts.restaurant", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Flayouts%2Frestaurant.blade.php&line=1", "ajax": false, "filename": "restaurant.blade.php", "line": "?"}}]}, "route": {"uri": "GET reviews", "middleware": "web", "controller": "App\\Http\\Controllers\\ReviewController@index", "namespace": null, "prefix": "", "where": [], "as": "reviews.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=26\" onclick=\"\">app/Http/Controllers/ReviewController.php:26-34</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00722999999********, "accumulated_duration_str": "7.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 28}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.412205, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReviewController.php:28", "source": "app/Http/Controllers/ReviewController.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=28", "ajax": false, "filename": "ReviewController.php", "line": "28"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select count(*) as aggregate from `reviews` where `status` = 'approved'", "type": "query", "params": [], "bindings": ["approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.431115, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "ReviewController.php:31", "source": "app/Http/Controllers/ReviewController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=31", "ajax": false, "filename": "ReviewController.php", "line": "31"}, "connection": "shopping67", "start_percent": 0, "width_percent": 31.259}, {"sql": "select * from `reviews` where `status` = 'approved' order by `created_at` desc limit 12 offset 0", "type": "query", "params": [], "bindings": ["approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4355738, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ReviewController.php:31", "source": "app/Http/Controllers/ReviewController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=31", "ajax": false, "filename": "ReviewController.php", "line": "31"}, "connection": "shopping67", "start_percent": 31.259, "width_percent": 5.671}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.812507, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "ReviewController.php:31", "source": "app/Http/Controllers/ReviewController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=31", "ajax": false, "filename": "ReviewController.php", "line": "31"}, "connection": "shopping67", "start_percent": 36.929, "width_percent": 58.921}, {"sql": "select * from `products` where `products`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\ReviewController.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.819344, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ReviewController.php:31", "source": "app/Http/Controllers/ReviewController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FReviewController.php&line=31", "ajax": false, "filename": "ReviewController.php", "line": "31"}, "connection": "shopping67", "start_percent": 95.851, "width_percent": 4.149}]}, "models": {"data": {"App\\Models\\Review": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FReview.php&line=1", "ajax": false, "filename": "Review.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/reviews\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/reviews", "status_code": "<pre class=sf-dump id=sf-dump-208699717 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-208699717\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-895040687 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-895040687\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-661441850 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-661441850\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/event_new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImxSSDQ3TVlNQXRlN3NYRmJ5b1NxZGc9PSIsInZhbHVlIjoiOFowS1dMbDJSY0V4ZnNDNzlhbExNRGdjMCtKa3Y1b2ZweXdHVTFuaHNKVkdKOEIrTzJaMGhKc1dvZDVaSHJQSEN5M3hXVy9ENVFnYlBsTllZd3hTR0tlT0Z3ZS9kSjExcXlsbGF6TCs0ZmpiOGtlYTlYMVlGalo1bFVnaVFoVlEiLCJtYWMiOiIxNTA3NDY3ZmY5MjZmOTk4ZmQ3OTQ1YWNjMzI1ZDJlMjhkMTc4YTlhYWE3NjVjYWM5ZTVmMjk5YzJmNzgxOTJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik5YZEJsbVVaVFJjRldsd251ZnkyY1E9PSIsInZhbHVlIjoiYXhRVDZvdkNsWmhaelBidzlDdzB3UHV6dWM5L2JjalJlbmZHeUJKb3VTT2RMWlVMcE43ZlZIQU8rRXlINk1xaGhnOWNqSHRmQngxaXVZK0JOMWd6K2Z0eWZlZ08yRTJ3eEJmZmk1enVUR2NrbkpaNnhhMmxJTDNRSjV3Zk92b1AiLCJtYWMiOiJkNGY1NjA5YmU4YTdhZGQ2MDVjODUwOGNkMTNlNTc4Zjk4YThlOTY0ODUwMjM1ZjY5ZTUzOTYwYmQ5ZjdmYjdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1523836113 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImI3SGFyeWFscGZlYUlFd3c5YlptT2c9PSIsInZhbHVlIjoiVldaSmFjY25sMzVJdWRmd1ZIOWU0M1VKVWFsV0pzY1hMTldoaGIwUjU2bTJsc0VpZjk2WjlaQ20rYjJrMDlpdENYeHdhSEtlbGlsZHBsNG9tVDRxejdSc2J3dmFldEVYSVhjWk1WN3F3V2ZZV3NOREFXOGN1ZXdEWEF0U1ExK3EiLCJtYWMiOiJjZGFhNGJiMThkMmQ0OGJhYTlmMTczNzAzMDMwNjVjNDI5ZjFhNTVhYmQyZTc4NTQ1YzAxN2M4NDMwMWRmN2NhIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlV4UG1YNUZMK0UxMXB5Q1ZtWGptbXc9PSIsInZhbHVlIjoibUMwQ09CNm91eHJoZnVBTWl5T3pzZkVlQ1ZpbHhLQnAzcnFFL1N4ZDJmYXE3UGVMbDh2VGRaY05lSFkydVNVM1lvaWlaZ2c2cmNtcS9DVjcxWitMZHlSdjJWdWU3UElQejB2d0NDVXJlRW00Mzk3LzZIMkNCcWZZRGd0Lzh1TVUiLCJtYWMiOiIwNWQxYWQ0YTI4YjExNDkxMGRiZjhhOThkYzRmZTY0YTMzZGMxZTI2MTVmZjI3YTJhOTFjMzI4YWEwM2FlOTljIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImI3SGFyeWFscGZlYUlFd3c5YlptT2c9PSIsInZhbHVlIjoiVldaSmFjY25sMzVJdWRmd1ZIOWU0M1VKVWFsV0pzY1hMTldoaGIwUjU2bTJsc0VpZjk2WjlaQ20rYjJrMDlpdENYeHdhSEtlbGlsZHBsNG9tVDRxejdSc2J3dmFldEVYSVhjWk1WN3F3V2ZZV3NOREFXOGN1ZXdEWEF0U1ExK3EiLCJtYWMiOiJjZGFhNGJiMThkMmQ0OGJhYTlmMTczNzAzMDMwNjVjNDI5ZjFhNTVhYmQyZTc4NTQ1YzAxN2M4NDMwMWRmN2NhIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlV4UG1YNUZMK0UxMXB5Q1ZtWGptbXc9PSIsInZhbHVlIjoibUMwQ09CNm91eHJoZnVBTWl5T3pzZkVlQ1ZpbHhLQnAzcnFFL1N4ZDJmYXE3UGVMbDh2VGRaY05lSFkydVNVM1lvaWlaZ2c2cmNtcS9DVjcxWitMZHlSdjJWdWU3UElQejB2d0NDVXJlRW00Mzk3LzZIMkNCcWZZRGd0Lzh1TVUiLCJtYWMiOiIwNWQxYWQ0YTI4YjExNDkxMGRiZjhhOThkYzRmZTY0YTMzZGMxZTI2MTVmZjI3YTJhOTFjMzI4YWEwM2FlOTljIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523836113\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-305684741 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305684741\", {\"maxDepth\":0})</script>\n"}}