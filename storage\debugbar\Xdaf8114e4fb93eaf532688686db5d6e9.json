{"__meta": {"id": "Xdaf8114e4fb93eaf532688686db5d6e9", "datetime": "2025-09-25 19:49:37", "utime": **********.472164, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758804575.62366, "end": **********.47219, "duration": 1.8485298156738281, "duration_str": "1.85s", "measures": [{"label": "Booting", "start": 1758804575.62366, "relative_start": 0, "end": 1758804575.811156, "relative_end": 1758804575.811156, "duration": 0.18749594688415527, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1758804575.811183, "relative_start": 0.18752288818359375, "end": **********.472192, "relative_end": 2.1457672119140625e-06, "duration": 1.6610090732574463, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21662928, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest, Closure", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=26\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:26-40</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00319, "accumulated_duration_str": "3.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 127}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 16, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 44}], "start": **********.170732, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:167", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=167", "ajax": false, "filename": "EloquentUserProvider.php", "line": "167"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 19, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 44}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 28}], "start": **********.18183, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "shopping67", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-750813614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-750813614\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1627963299 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627963299\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-643100033 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">98</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InFINkhma1FIZlUyMTdjTGR4N0liL1E9PSIsInZhbHVlIjoickhyaGF2ZGZOcU42UDFBN0tSSlpWd3RCTlREUGpKUXBCNjUrNUlFNzYvYitpSloxcHBQMXdNUVY4dmhMaFNheDBwZnAwazV5YytqOWQ5dmRqOVZhdE96bWlyQTZ6eUZtWEp2VjRBdmdxYThpZTJSNkxoaDU3c0xNTVFlcWxSREIiLCJtYWMiOiIzY2Y4MjI3MTAwYTgzZjIzMWYwMTE5OWIxNTVkZWJjNWFlMjdhOGIzZTZmNWJlM2IyYjQxMzVlMzhhZWQ5Y2Y3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdRRHd3YXY5ZUlhMStVTENVS1U0M3c9PSIsInZhbHVlIjoib2xQZW5OQnBFeUpxcFFORVZ2ZDE1Sm5OcDVzNE5XOHhsWDJHdjNLcUo0amVDUWhIYThXdVhjS0JPU2UwWHBOTkFjWkVPV1VYNFZ2M1M1Z3ZyTnBjRGQyS2ZKSmpZWWl2L3l1Ym5oTlZNSEdsL20zUTUzVFZ2R2lCUmZPcE1YK00iLCJtYWMiOiJhN2EwNjVjN2QxYTQ3NmJkNTQzZTc5ZjAxNmNlOTdlZDdjMDJkNTk4MjcwYWRiY2ViOWZiYmZmNzk5M2ZhMTgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643100033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2069873788 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069873788\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1458409634 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImswbmxsWnZGeTl0WEVMOEtJVllZaHc9PSIsInZhbHVlIjoiVFNnWjhJdHUyVFdTMm53Q0VCYjludnQ0ZmxCM3FJblB0amtxRnFXU0pFbVNFZzVzL1hza0dVUTI4Y0VUUFN2cktodWEya2pKMFNzV0NvVU1HVllwNDZHNzNvazV0Z2NlbHZFa3IvL3RQckt1V3MwUlVGQ29ZNithQi9CeVo3OUEiLCJtYWMiOiIyZDE0ZjYxZGQxMDkyZWVmMzIzYTEyMmNmOGIxODVkOWI4OGFmZDFiYTk3Y2ZiNzdkM2M4MTA3ZTk5ZWU3ZTBhIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Inh5RFlaQ1J0cTdsWjB4RkplVDdmUGc9PSIsInZhbHVlIjoiUWR2bUlKSkZFQSt0SHcrM2x4NmhDMFY4ZlF3L1g2QmU4d08rdjBkc1NVbjlQbFRDT2lNd1VGTldSZm1QU0UvTkpMVHMySTA2NTc2UVRKdytIVDJvaVNjeTBWMGMvQ2liWk9aMlFJWkY1VVdLRTFGQU52bk5pS2hVSFF4R1FBUUsiLCJtYWMiOiJjZjA1YzY4NTUyMTE2OTU3NWRjM2M5YTA0ZGI0ZTkzMzg3ZTkxZGFlY2M2OTBmMmY5Nzg3NGExNjhlMjRmNGIzIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImswbmxsWnZGeTl0WEVMOEtJVllZaHc9PSIsInZhbHVlIjoiVFNnWjhJdHUyVFdTMm53Q0VCYjludnQ0ZmxCM3FJblB0amtxRnFXU0pFbVNFZzVzL1hza0dVUTI4Y0VUUFN2cktodWEya2pKMFNzV0NvVU1HVllwNDZHNzNvazV0Z2NlbHZFa3IvL3RQckt1V3MwUlVGQ29ZNithQi9CeVo3OUEiLCJtYWMiOiIyZDE0ZjYxZGQxMDkyZWVmMzIzYTEyMmNmOGIxODVkOWI4OGFmZDFiYTk3Y2ZiNzdkM2M4MTA3ZTk5ZWU3ZTBhIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Inh5RFlaQ1J0cTdsWjB4RkplVDdmUGc9PSIsInZhbHVlIjoiUWR2bUlKSkZFQSt0SHcrM2x4NmhDMFY4ZlF3L1g2QmU4d08rdjBkc1NVbjlQbFRDT2lNd1VGTldSZm1QU0UvTkpMVHMySTA2NTc2UVRKdytIVDJvaVNjeTBWMGMvQ2liWk9aMlFJWkY1VVdLRTFGQU52bk5pS2hVSFF4R1FBUUsiLCJtYWMiOiJjZjA1YzY4NTUyMTE2OTU3NWRjM2M5YTA0ZGI0ZTkzMzg3ZTkxZGFlY2M2OTBmMmY5Nzg3NGExNjhlMjRmNGIzIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458409634\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-541370445 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541370445\", {\"maxDepth\":0})</script>\n"}}