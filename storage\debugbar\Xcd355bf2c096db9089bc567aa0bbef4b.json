{"__meta": {"id": "Xcd355bf2c096db9089bc567aa0bbef4b", "datetime": "2025-09-25 19:49:03", "utime": 1758804543.69511, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758804535.17469, "end": 1758804543.695134, "duration": 8.5204439163208, "duration_str": "8.52s", "measures": [{"label": "Booting", "start": 1758804535.17469, "relative_start": 0, "end": 1758804536.370264, "relative_end": 1758804536.370264, "duration": 1.1955740451812744, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1758804536.370575, "relative_start": 1.1958849430084229, "end": 1758804543.695136, "relative_end": 2.1457672119140625e-06, "duration": 7.32456111907959, "duration_str": "7.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21658688, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "index", "param_count": null, "params": [], "start": 1758804542.126661, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.phpindex", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\IndexController@index", "namespace": null, "prefix": "", "where": [], "as": "index2", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=22\" onclick=\"\">app/Http/Controllers/IndexController.php:22-56</a>"}, "queries": {"nb_statements": 35, "nb_failed_statements": 0, "accumulated_duration": 0.24466999999999997, "accumulated_duration_str": "245ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 8, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.179813, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "SHOW TABLES LIKE 'reviews'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2255778, "duration": 0.*****************, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping67", "start_percent": 0, "width_percent": 43.786}, {"sql": "SHOW COLUMNS FROM reviews", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 99}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 87}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3355808, "duration": 0.01569, "duration_str": "15.69ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:99", "source": "app/Http/Controllers/IndexController.php:99", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=99", "ajax": false, "filename": "IndexController.php", "line": "99"}, "connection": "shopping67", "start_percent": 43.786, "width_percent": 6.413}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1629322, "duration": 0.04118, "duration_str": "41.18ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:29", "source": "app/Http/Controllers/IndexController.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=29", "ajax": false, "filename": "IndexController.php", "line": "29"}, "connection": "shopping67", "start_percent": 50.198, "width_percent": 16.831}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2619748, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:30", "source": "app/Http/Controllers/IndexController.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=30", "ajax": false, "filename": "IndexController.php", "line": "30"}, "connection": "shopping67", "start_percent": 67.029, "width_percent": 1.03}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.267131, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:31", "source": "app/Http/Controllers/IndexController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=31", "ajax": false, "filename": "IndexController.php", "line": "31"}, "connection": "shopping67", "start_percent": 68.059, "width_percent": 0.27}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.270356, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping67", "start_percent": 68.329, "width_percent": 0.213}, {"sql": "select * from `products` where `products`.`category_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.379042, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping67", "start_percent": 68.541, "width_percent": 0.294}, {"sql": "select * from `event_news` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4054081, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:36", "source": "app/Http/Controllers/IndexController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=36", "ajax": false, "filename": "IndexController.php", "line": "36"}, "connection": "shopping67", "start_percent": 68.836, "width_percent": 1.185}, {"sql": "select * from `products` order by RAND() limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4110339, "duration": 0.01354, "duration_str": "13.54ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 70.021, "width_percent": 5.534}, {"sql": "select * from `categories` where `categories`.`id` in (1, 2, 3, 4, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5273669, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 75.555, "width_percent": 0.311}, {"sql": "select * from `brands` where `brands`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.555499, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 75.865, "width_percent": 1.484}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.2363641, "duration": 0.01225, "duration_str": "12.25ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 77.349, "width_percent": 5.007}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 6 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["6", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.282127, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 82.356, "width_percent": 0.315}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.284833, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 82.671, "width_percent": 1.304}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.2902849, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 83.974, "width_percent": 0.159}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.2923698, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 84.134, "width_percent": 1.386}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.2980618, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 85.519, "width_percent": 0.168}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.300263, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 85.687, "width_percent": 1.312}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.305743, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 86.999, "width_percent": 0.204}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.308063, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 87.203, "width_percent": 1.733}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.315619, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 88.936, "width_percent": 0.217}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.3179429, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 89.153, "width_percent": 1.234}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.3231199, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 90.387, "width_percent": 0.163}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.325226, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 90.551, "width_percent": 1.283}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.330653, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 91.834, "width_percent": 0.176}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 663}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.332901, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 92.01, "width_percent": 1.222}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 663}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.338142, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 93.232, "width_percent": 0.176}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.340599, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 93.407, "width_percent": 1.279}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 5 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["5", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.346327, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 94.687, "width_percent": 0.188}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.349735, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 94.875, "width_percent": 1.831}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 3 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["3", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.357244, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 96.706, "width_percent": 0.237}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.3597538, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 96.943, "width_percent": 1.316}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 4 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["4", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.3652148, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 98.259, "width_percent": 0.168}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1758804543.367519, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 98.426, "width_percent": 1.369}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 2 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["2", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758804543.373943, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 99.796, "width_percent": 0.204}]}, "models": {"data": {"App\\Models\\Category": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Product": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Brand": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 71, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-589475790 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-589475790\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-726235854 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-726235854\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-71995686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71995686\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1869156101 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869156101\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-893043220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-893043220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1007747571 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjBWM3FPT29RNXBJYkZEa0Z0UlhYNlE9PSIsInZhbHVlIjoidkxqSDFrRC80cnFnVUNnMEloQ1NvajIydkFTQmZ4d0h4bS93cithYVBta3dEaEV6OTZiTDd3Vm9LZUFTM3NpMTQ3UHBQY3B5b2VQeWlrelFuWXdFcjF4bTVEdTNCR0hjY2tqa0hLaFY5aERXZHFhTW5LYUdwbkh5R0xnS1BOTnciLCJtYWMiOiI0MzJmYmQ4YzRkNzkwNjA3ZmM3M2JkNDU1YjkwZjZjM2ZhZDI1YzUxZWViNDM1MmJkYTU0NGE3NDExMmNjYTE3IiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im5XQU5pWElYWm5PRzBhbDN4bHR5REE9PSIsInZhbHVlIjoiSkUzVEJvaTlRNjMzTHd4WGk4WWRybEQzQ3R3THN6T1Q2ek1sSE5wMTNzWlNjeUFLWUpDUHZZL2hrc3dJNlczanF5VDlwNW50bzdLRndoT2IxRzFvNGtyVGFQdlYyL1JBc3FYQWVHUkpaTnI3YzBhQXk1c0pMV3F3NDQ2YjY4UFQiLCJtYWMiOiI1ZjQyN2IyODQzNmY5ZGU2ZTQ2MDNiNWY5MWM4YTI4ZjcyMzgwNjAwNjllM2EzM2I1ODczN2NkMmFhYTc1MTRiIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjBWM3FPT29RNXBJYkZEa0Z0UlhYNlE9PSIsInZhbHVlIjoidkxqSDFrRC80cnFnVUNnMEloQ1NvajIydkFTQmZ4d0h4bS93cithYVBta3dEaEV6OTZiTDd3Vm9LZUFTM3NpMTQ3UHBQY3B5b2VQeWlrelFuWXdFcjF4bTVEdTNCR0hjY2tqa0hLaFY5aERXZHFhTW5LYUdwbkh5R0xnS1BOTnciLCJtYWMiOiI0MzJmYmQ4YzRkNzkwNjA3ZmM3M2JkNDU1YjkwZjZjM2ZhZDI1YzUxZWViNDM1MmJkYTU0NGE3NDExMmNjYTE3IiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im5XQU5pWElYWm5PRzBhbDN4bHR5REE9PSIsInZhbHVlIjoiSkUzVEJvaTlRNjMzTHd4WGk4WWRybEQzQ3R3THN6T1Q2ek1sSE5wMTNzWlNjeUFLWUpDUHZZL2hrc3dJNlczanF5VDlwNW50bzdLRndoT2IxRzFvNGtyVGFQdlYyL1JBc3FYQWVHUkpaTnI3YzBhQXk1c0pMV3F3NDQ2YjY4UFQiLCJtYWMiOiI1ZjQyN2IyODQzNmY5ZGU2ZTQ2MDNiNWY5MWM4YTI4ZjcyMzgwNjAwNjllM2EzM2I1ODczN2NkMmFhYTc1MTRiIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007747571\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-789846448 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789846448\", {\"maxDepth\":0})</script>\n"}}