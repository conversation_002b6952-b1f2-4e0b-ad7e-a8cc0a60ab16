{"__meta": {"id": "X624ce863e8e84f5e9d457289c6180de8", "datetime": "2025-09-25 19:50:10", "utime": 1758804610.285642, "method": "GET", "uri": "/admin/members", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.746713, "end": 1758804610.285676, "duration": 0.5389630794525146, "duration_str": "539ms", "measures": [{"label": "Booting", "start": **********.746713, "relative_start": 0, "end": **********.89515, "relative_end": **********.89515, "duration": 0.1484370231628418, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.895168, "relative_start": 0.14845514297485352, "end": 1758804610.28568, "relative_end": 4.0531158447265625e-06, "duration": 0.39051198959350586, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21241544, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "admin.members.index", "param_count": null, "params": [], "start": **********.994575, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/members/index.blade.phpadmin.members.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fmembers%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": 1758804610.263674, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "components.delete-confirmation-modal", "param_count": null, "params": [], "start": 1758804610.264258, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/components/delete-confirmation-modal.blade.phpcomponents.delete-confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fcomponents%2Fdelete-confirmation-modal.blade.php&line=1", "ajax": false, "filename": "delete-confirmation-modal.blade.php", "line": "?"}}, {"name": "admin.admin_master", "param_count": null, "params": [], "start": 1758804610.264796, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/admin_master.blade.phpadmin.admin_master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fadmin_master.blade.php&line=1", "ajax": false, "filename": "admin_master.blade.php", "line": "?"}}, {"name": "sweetalert::alert", "param_count": null, "params": [], "start": 1758804610.265591, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}}, {"name": "admin.body.header", "param_count": null, "params": [], "start": 1758804610.266272, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.phpadmin.body.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "admin.body.slidemenu", "param_count": null, "params": [], "start": 1758804610.269936, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/slidemenu.blade.phpadmin.body.slidemenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fslidemenu.blade.php&line=1", "ajax": false, "filename": "slidemenu.blade.php", "line": "?"}}, {"name": "admin.body.footer", "param_count": null, "params": [], "start": 1758804610.270612, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/footer.blade.phpadmin.body.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/members", "middleware": "web, auth, admin, Closure", "controller": "App\\Http\\Controllers\\Admin\\MemberController@index", "namespace": null, "prefix": "", "where": [], "as": "admin.members.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=15\" onclick=\"\">app/Http/Controllers/Admin/MemberController.php:15-63</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.006059999999999999, "accumulated_duration_str": "6.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.952763, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.9631588, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 0, "width_percent": 32.673}, {"sql": "select count(*) as aggregate from `users` where `role` = 'customer'", "type": "query", "params": [], "bindings": ["customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/MemberController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\MemberController.php", "line": 45}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.96945, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MemberController.php:45", "source": "app/Http/Controllers/Admin/MemberController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=45", "ajax": false, "filename": "MemberController.php", "line": "45"}, "connection": "shopping67", "start_percent": 32.673, "width_percent": 6.766}, {"sql": "select * from `users` where `role` = 'customer' order by `created_at` desc limit 15 offset 0", "type": "query", "params": [], "bindings": ["customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/MemberController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\MemberController.php", "line": 45}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9722111, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MemberController.php:45", "source": "app/Http/Controllers/Admin/MemberController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=45", "ajax": false, "filename": "MemberController.php", "line": "45"}, "connection": "shopping67", "start_percent": 39.439, "width_percent": 7.261}, {"sql": "select count(*) as aggregate from `users` where `role` = 'customer'", "type": "query", "params": [], "bindings": ["customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/MemberController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\MemberController.php", "line": 49}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.975569, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MemberController.php:49", "source": "app/Http/Controllers/Admin/MemberController.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=49", "ajax": false, "filename": "MemberController.php", "line": "49"}, "connection": "shopping67", "start_percent": 46.7, "width_percent": 5.446}, {"sql": "select count(*) as aggregate from `users` where `role` = 'customer' and `gender` = 'ชาย'", "type": "query", "params": [], "bindings": ["customer", "ชาย"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/MemberController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\MemberController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9781299, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MemberController.php:50", "source": "app/Http/Controllers/Admin/MemberController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=50", "ajax": false, "filename": "MemberController.php", "line": "50"}, "connection": "shopping67", "start_percent": 52.145, "width_percent": 7.096}, {"sql": "select count(*) as aggregate from `users` where `role` = 'customer' and `gender` = 'หญิง'", "type": "query", "params": [], "bindings": ["customer", "หญิง"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/MemberController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\MemberController.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.980993, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MemberController.php:51", "source": "app/Http/Controllers/Admin/MemberController.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=51", "ajax": false, "filename": "MemberController.php", "line": "51"}, "connection": "shopping67", "start_percent": 59.241, "width_percent": 7.426}, {"sql": "select count(*) as aggregate from `users` where `role` = 'customer' and `created_at` >= '2025-08-26 19:50:09'", "type": "query", "params": [], "bindings": ["customer", "2025-08-26 19:50:09"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/MemberController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\MemberController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.984255, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MemberController.php:54", "source": "app/Http/Controllers/Admin/MemberController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FMemberController.php&line=54", "ajax": false, "filename": "MemberController.php", "line": "54"}, "connection": "shopping67", "start_percent": 66.667, "width_percent": 7.591}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`user_id` = 1 and `reviews`.`user_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": "view", "name": "admin.members.index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/members/index.blade.php", "line": 244}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": 1758804610.259752, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "admin.members.index:244", "source": "view::admin.members.index:244", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fmembers%2Findex.blade.php&line=244", "ajax": false, "filename": "index.blade.php", "line": "244"}, "connection": "shopping67", "start_percent": 74.257, "width_percent": 17.822}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "admin.body.header", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": 1758804610.26683, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "admin.body.header:32", "source": "view::admin.body.header:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=32", "ajax": false, "filename": "header.blade.php", "line": "32"}, "connection": "shopping67", "start_percent": 92.079, "width_percent": 7.921}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/members\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/members", "status_code": "<pre class=sf-dump id=sf-dump-1972266337 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1972266337\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1466666549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1466666549\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1861991774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1861991774\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1904205166 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/u1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFaWGd6eUlmRXlOWEpGZk1CaEdGTkE9PSIsInZhbHVlIjoiR1NyS2Fpb3dpQ3krVG1LQXB1YXRXVUExWDIrc2t3TXQ5MUtIT2FOSzlXTmNqRTFwQ1Y4NFV5MXJSbWZKUTVwWVJNNUJVYkNoR1FIQ1VucWE1WTdHR0kyOUhIY1BEbXc1V2ZsRTdEVFJsSEdjQ2d0WVdQNEJ1MmRqdjFQelZIVFYiLCJtYWMiOiJlYjdhMzlkZWY2ODQyOWU0MDllMWM3MzllODViOThhYTRhN2E5ODFlNDYxMjVlMzlmYmU2Zjc4NzA4NzUwZjRlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhhckZQVThSUzcvL3ZES1gxOWllQVE9PSIsInZhbHVlIjoiQVl3OTlYcnVyZFExOVZlRDBEbUE5aFdqQXZsL3BOamFzVHJnSFNsTlNnRHhlemdJSGdhU0lQMUpGUGFhNHM1cENyYlhKQXlZSGRvL1hSaUdudVV5MHhndXhFVXVqWGNWVCtCZ3NUbVl6L3lYRkNtWmRTYWkvY1ZjeHNxdXFScG4iLCJtYWMiOiJiZTYxMjBjNDI5Y2VkMGRmMzZhYmExMTRkYWE2OGNmN2ViNmY5MmE0NWQ5ZmRkOGQzOGVmM2IwYzA4NWZmM2U4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904205166\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-859601159 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WMTIWfNahNQIltL7Sh8nxhpUhpWn05qNcCODNhVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859601159\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-965204933 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:50:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjFQdU1HM0RiNmJVRjZScnkyc2NmUVE9PSIsInZhbHVlIjoiWXZIQWFSVnU5N2hnT1VtM2hWZ21VMWF2aEFzMldwTFlnb3IzdGprWHIrek90eXA1akxVOHRsbFVPM2tBUExLSnZwQ0gzRlpHK2lkQ3JqMXJVMTZLY2x0aDVNckZWQXk4WjhxbFpGTGpFdW9oRldEVUFsaENkS0M5UlRJbjFKRW0iLCJtYWMiOiJmODU4OGRhODNhMmUzOWEwZmFlZjQ4NjEzZDJhZjRlYzc0NzA3OTQ3ZWI0YzZiMTI5ZmI3ZjRhZmMwOTEzOGZkIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:50:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkkyMXRQTjJKelo0cy9OT1V6RjlTd1E9PSIsInZhbHVlIjoiY2U3cnNTRWhSSjlLQlk4Z2tJVm52amJVRjdET2VOYnYzd3FKaWZDbWdSMjRHSXZmdEVaaHhEVTdxbCtwcU14WklWcmk2QndkQnNmQlhWQm1ieE9OV2RRa1lrU3J1VmpISHNHWGVHa2RCeExqbXl2dFM3ajVUQTc1dHhMMjlCdkwiLCJtYWMiOiIzZDk2OTQwN2FkOWZhZjEyYTYwNDFkNjY0MDlkNjk1ZTMwMTNkZTMwMDM2MmZhNDA5Y2MwMjg5MTY4Y2YwM2M3IiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:50:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFQdU1HM0RiNmJVRjZScnkyc2NmUVE9PSIsInZhbHVlIjoiWXZIQWFSVnU5N2hnT1VtM2hWZ21VMWF2aEFzMldwTFlnb3IzdGprWHIrek90eXA1akxVOHRsbFVPM2tBUExLSnZwQ0gzRlpHK2lkQ3JqMXJVMTZLY2x0aDVNckZWQXk4WjhxbFpGTGpFdW9oRldEVUFsaENkS0M5UlRJbjFKRW0iLCJtYWMiOiJmODU4OGRhODNhMmUzOWEwZmFlZjQ4NjEzZDJhZjRlYzc0NzA3OTQ3ZWI0YzZiMTI5ZmI3ZjRhZmMwOTEzOGZkIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:50:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkkyMXRQTjJKelo0cy9OT1V6RjlTd1E9PSIsInZhbHVlIjoiY2U3cnNTRWhSSjlLQlk4Z2tJVm52amJVRjdET2VOYnYzd3FKaWZDbWdSMjRHSXZmdEVaaHhEVTdxbCtwcU14WklWcmk2QndkQnNmQlhWQm1ieE9OV2RRa1lrU3J1VmpISHNHWGVHa2RCeExqbXl2dFM3ajVUQTc1dHhMMjlCdkwiLCJtYWMiOiIzZDk2OTQwN2FkOWZhZjEyYTYwNDFkNjY0MDlkNjk1ZTMwMTNkZTMwMDM2MmZhNDA5Y2MwMjg5MTY4Y2YwM2M3IiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:50:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965204933\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1955525980 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/admin/members</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955525980\", {\"maxDepth\":0})</script>\n"}}