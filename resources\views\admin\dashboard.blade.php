@extends('admin.admin_master')
@section('content')

<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">

            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0 page-title">🍽️ แดชบอร์ดจัดการร้านอาหาร</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">หน้าแรก</a></li>
                                <li class="breadcrumb-item active">แดชบอร์ด</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Welcome Card -->
            <div class="row">
                <div class="col-12">
                    <div class="card" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; border-radius: 20px;">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h3 class="text-white mb-3">🎯 ยินดีต้อนรับสู่ระบบจัดการ BBC HOME KITCHEN</h3>
                                    <p class="text-white-50 mb-0">จัดการเมนูอาหาร คำสั่งซื้อ และติดตามยอดขายได้ที่นี่</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div style="font-size: 4rem;">📊</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            🍽️
                        </div>
                        <h3 class="text-primary">{{ $totalProducts }}</h3>
                        <p class="text-muted mb-0">เมนูอาหารทั้งหมด</p>
                        <a href="{{ route('product.index') }}" class="btn btn-sm btn-outline-primary mt-2">
                            <i class="ri-arrow-right-line me-1"></i>จัดการ
                        </a>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #00b894, #00cec9);">
                            📂
                        </div>
                        <h3 class="text-success">{{ $totalCategories }}</h3>
                        <p class="text-muted mb-0">หมวดหมู่ทั้งหมด</p>
                        <a href="{{ route('category.index') }}" class="btn btn-sm btn-outline-success mt-2">
                            <i class="ri-arrow-right-line me-1"></i>จัดการ
                        </a>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #fdcb6e, #e17055);">
                            🏷️
                        </div>
                        <h3 class="text-warning">{{ $totalBrands }}</h3>
                        <p class="text-muted mb-0">แบรนด์ทั้งหมด</p>
                        <a href="{{ route('brand.index') }}" class="btn btn-sm btn-outline-warning mt-2">
                            <i class="ri-arrow-right-line me-1"></i>จัดการ
                        </a>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #74b9ff, #0984e3);">
                            📰
                        </div>
                        <h3 class="text-info">{{ $totalEvents }}</h3>
                        <p class="text-muted mb-0">ข่าวสารทั้งหมด</p>
                        <a href="{{ route('event.index') }}" class="btn btn-sm btn-outline-info mt-2">
                            <i class="ri-arrow-right-line me-1"></i>จัดการ
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Products and Management -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4 class="card-title mb-0">📋 เมนูอาหารล่าสุด</h4>
                                <a href="{{ route('product.index') }}" class="btn btn-primary">
                                    <i class="ri-list-check me-2"></i>จัดการเมนูทั้งหมด
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-nowrap mb-0">
                                    <thead>
                                        <tr>
                                            <th>รูปภาพ</th>
                                            <th>รหัสเมนู</th>
                                            <th>ชื่อเมนู</th>
                                            <th>หมวดหมู่</th>
                                            <th>ราคา</th>
                                            <th>สถานะ</th>
                                            <th>การจัดการ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($recentProducts as $index => $product)
                                        <tr>
                                            <td>
                                                @if ($product->picture && $product->picture !== 'nopic.png' && file_exists(public_path('storage/products/' . $product->picture)))
                                                    <img src="{{asset('storage/products/'.$product->picture)}}" alt="{{$product->proname}}"
                                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                                                @else
                                                    <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ff9500, #ff6b00);
                                                               border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; color: white;">
                                                        🍽️
                                                    </div>
                                                @endif
                                            </td>
                                            <td class="fw-bold">#{{ str_pad($product->id, 3, '0', STR_PAD_LEFT) }}</td>
                                            <td>
                                                <h6 class="mb-1">{{ $product->proname }}</h6>
                                                <small class="text-muted">{{ Str::limit($product->prodetail, 30) }}</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-soft-info">{{ $product->category->name ?? 'ไม่ระบุ' }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">{{ number_format($product->price, 0) }} บาท</span>
                                            </td>
                                            <td>
                                                <span class="badge badge-soft-success">พร้อมขาย</span>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a href="{{ route('products.edit', $product->id) }}" class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                                        <i class="ri-edit-line"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-info" title="ดูรายละเอียด"
                                                            data-bs-toggle="modal" data-bs-target="#productModal{{ $product->id }}">
                                                        <i class="ri-eye-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="7" class="text-center text-muted py-4">
                                                <div class="mb-3" style="font-size: 3rem;">🍽️</div>
                                                <h5>ยังไม่มีเมนูอาหาร</h5>
                                                <p class="mb-0">เริ่มต้นเพิ่มเมนูอาหารแรกของคุณ</p>
                                                <a href="{{ route('product.create') }}" class="btn btn-primary mt-2">
                                                    <i class="ri-add-circle-line me-2"></i>เพิ่มเมนูใหม่
                                                </a>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            @if($recentProducts->count() > 0)
                            <div class="text-center mt-3">
                                <a href="{{ route('product.index') }}" class="btn btn-outline-primary">
                                    <i class="ri-arrow-right-line me-2"></i>ดูเมนูทั้งหมด ({{ $totalProducts }} รายการ)
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">⚡ การดำเนินการด่วน</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('product.create') }}" class="btn btn-primary w-100 py-3">
                                        <i class="ri-add-circle-line me-2"></i>
                                        เพิ่มเมนูใหม่
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('product.index') }}" class="btn btn-outline-primary w-100 py-3">
                                        <i class="ri-restaurant-line me-2"></i>
                                        จัดการเมนู
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('category.create') }}" class="btn btn-success w-100 py-3">
                                        <i class="ri-list-check-2 me-2"></i>
                                        เพิ่มหมวดหมู่
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('event.create') }}" class="btn btn-warning w-100 py-3">
                                        <i class="ri-newspaper-line me-2"></i>
                                        เพิ่มข่าวสาร
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('menu.cards') }}" class="btn btn-outline-info w-100 py-3">
                                        <i class="ri-eye-line me-2"></i>
                                        ดูเมนูหน้าเว็บ
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('index2') }}" class="btn btn-info w-100 py-3">
                                        <i class="ri-external-link-line me-2"></i>
                                        ดูเว็บไซต์
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- Product Detail Modals -->
@foreach($recentProducts as $product)
<div class="modal fade" id="productModal{{ $product->id }}" tabindex="-1" aria-labelledby="productModalLabel{{ $product->id }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white;">
                <h5 class="modal-title fw-bold" id="productModalLabel{{ $product->id }}">
                    🍽️ {{ $product->proname }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        @if($product->picture && $product->picture !== 'nopic.png' && file_exists(public_path('storage/products/' . $product->picture)))
                            <img src="{{ asset('storage/products/' . $product->picture) }}" alt="{{ $product->proname }}"
                                 class="img-fluid rounded" style="max-height: 300px; width: 100%; object-fit: cover;">
                        @else
                            <div class="d-flex align-items-center justify-content-center rounded"
                                 style="height: 300px; background: linear-gradient(135deg, #ffeaa7, #fdcb6e); font-size: 4rem;">
                                🍽️
                            </div>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <div class="product-info">
                            <div class="mb-3">
                                <label class="form-label fw-bold">รหัสเมนู:</label>
                                <span class="text-muted">#{{ str_pad($product->id, 3, '0', STR_PAD_LEFT) }}</span>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">ราคา:</label>
                                <span class="text-success fw-bold fs-5">{{ number_format($product->price, 0) }} บาท</span>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">หมวดหมู่:</label>
                                <span class="badge badge-soft-info">{{ $product->category->name ?? 'ไม่ระบุ' }}</span>
                            </div>

                            @if($product->brand)
                            <div class="mb-3">
                                <label class="form-label fw-bold">แบรนด์:</label>
                                <span class="text-muted">{{ $product->brand->brand_name }}</span>
                            </div>
                            @endif

                            <div class="mb-3">
                                <label class="form-label fw-bold">สถานะ:</label>
                                <span class="badge badge-soft-success">พร้อมขาย</span>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">วันที่เพิ่ม:</label>
                                <span class="text-muted">{{ $product->created_at->format('d/m/Y H:i') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <label class="form-label fw-bold">รายละเอียดเมนู:</label>
                        <div class="p-3 bg-light rounded">
                            <p class="mb-0">{{ $product->prodetail ?: 'ไม่มีรายละเอียด' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ri-close-line me-2"></i>ปิด
                </button>
                <a href="{{ route('products.edit', $product->id) }}" class="btn btn-primary">
                    <i class="ri-edit-line me-2"></i>แก้ไขเมนู
                </a>
            </div>
        </div>
    </div>
</div>
@endforeach

@endsection
