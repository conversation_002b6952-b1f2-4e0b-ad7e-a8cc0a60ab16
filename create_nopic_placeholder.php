<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "🖼️ สร้างไฟล์ placeholder สำหรับรูปภาพ...\n\n";
    
    // สร้างโฟลเดอร์ถ้ายังไม่มี
    $directories = [
        storage_path('app/public/products'),
        storage_path('app/public/categories'),
        storage_path('app/public/brands'),
        storage_path('app/public/event_news')
    ];
    
    foreach($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "📁 สร้างโฟลเดอร์: {$dir}\n";
        }
    }
    
    // สร้างรูป placeholder แบบง่าย (1x1 pixel PNG)
    $placeholderData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==');
    
    // สร้าง nopic.png ในแต่ละโฟลเดอร์
    $placeholderFiles = [
        storage_path('app/public/products/nopic.png'),
        storage_path('app/public/categories/nopic.png'),
        storage_path('app/public/brands/nopic.png'),
        storage_path('app/public/event_news/nopic.png')
    ];
    
    foreach($placeholderFiles as $file) {
        file_put_contents($file, $placeholderData);
        echo "✅ สร้างไฟล์: " . basename(dirname($file)) . "/nopic.png\n";
    }
    
    echo "\n🔧 แก้ไขข้อมูลในฐานข้อมูล...\n";

    // แก้ไขข้อมูลหมวดหมู่ที่มีปัญหา
    
    $fixedCategories = \Illuminate\Support\Facades\DB::table('categories')
        ->where('image_path', 'categories/')
        ->update(['image_path' => 'nopic.png']);
    
    echo "✅ แก้ไขข้อมูลหมวดหมู่: {$fixedCategories} รายการ\n";
    
    // ตรวจสอบผลลัพธ์
    echo "\n🔍 ตรวจสอบผลลัพธ์:\n";
    
    foreach($placeholderFiles as $file) {
        $exists = file_exists($file);
        $size = $exists ? filesize($file) : 0;
        echo "  " . ($exists ? '✅' : '❌') . " " . basename(dirname($file)) . "/nopic.png ({$size} bytes)\n";
    }
    
    echo "\n✅ สร้างไฟล์ placeholder เสร็จสิ้น!\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
