@extends('admin.admin_master')

@section('content')
<!-- CSRF Token for JavaScript -->
<meta name="csrf-token" content="{{ csrf_token() }}"
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0 font-size-18">📰 จัดการข่าวประชาสัมพันธ์</h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                            <li class="breadcrumb-item active">ข่าวประชาสัมพันธ์</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="card-title mb-0">📢 รายการข่าวประชาสัมพันธ์ทั้งหมด</h4>
                            <a href="{{route('event.create')}}" class="btn btn-success">
                                <i class="ri-add-circle-line me-2"></i>เพิ่มข่าวใหม่
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="ri-check-circle-line me-2"></i>{{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-primary">📊 สรุปข้อมูล</h5>
                                    <p class="text-muted mb-0">จำนวนข่าวทั้งหมด: <strong>{{$countEvent}}</strong> รายการ</p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">อัปเดตล่าสุด: {{ now()->format('d/m/Y H:i:s') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>ลำดับ</th>
                                        <th>หัวข้อข่าว</th>
                                        <th>รายละเอียดข่าว</th>
                                        <th>วันที่เผยแพร่</th>
                                        <th>ภาพ</th>
                                        <th>แก้ไข</th>
                                        
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($eventNews as $item => $ev)
                                    <tr class="table-light">
                                        <th scope="row">
                                            <span class="badge bg-primary">{{$ev->id}}</span>
                                        </th>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{$ev->title}}</h6>
                                                    <small class="text-muted">
                                                        สร้างเมื่อ: {{ $ev->created_at ? $ev->created_at->format('d/m/Y H:i') : '-' }}
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="max-width: 300px;">
                                                {{ Str::limit($ev->description, 100) }}
                                                @if(strlen($ev->description) > 100)
                                                    <br><small class="text-muted">... และอีก {{ strlen($ev->description) - 100 }} ตัวอักษร</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ $ev->event_date ? \Carbon\Carbon::parse($ev->event_date)->format('d/m/Y') : '-' }}
                                            </span>
                                            @if($ev->event_date)
                                                <br><small class="text-muted">
                                                    {{ \Carbon\Carbon::parse($ev->event_date)->diffForHumans() }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($ev->pic && file_exists(public_path('storage/event_news/' . $ev->pic)))
                                                <img src="{{asset('storage/event_news/'.$ev->pic)}}" alt="{{$ev->title}}" style="width: 100px; height: 70px; object-fit: cover; border-radius: 8px;">
                                            @else
                                                <div class="text-center text-white rounded" style="width: 100px; height: 70px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #ff6b6b, #ffa500);">
                                                    <i class="ri-news-line" style="font-size: 1.5rem;"></i>
                                                </div>
                                            @endif
                                        </td>

                                        <td>
                                            <a href="{{ route('event.edit', $ev->id) }}"
                                               class="btn btn-warning btn-sm waves-effect waves-light"
                                               title="แก้ไขข่าว">
                                                <i class="ri-edit-line"></i> แก้ไข
                                            </a>
                                        </td>

                                      
                                    </tr>

                                    @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ri-newspaper-line" style="font-size: 3rem;"></i>
                                                <h5 class="mt-2">ยังไม่มีข่าวประชาสัมพันธ์</h5>
                                                <p>เริ่มต้นสร้างข่าวประชาสัมพันธ์แรกของคุณ</p>
                                                <a href="{{route('event.create')}}" class="btn btn-primary">
                                                    <i class="ri-add-circle-line me-2"></i>เพิ่มข่าวใหม่
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        @if($eventNews->hasPages())
                            <div class="row mt-3">
                                <div class="col-sm-12 col-md-5">
                                    <div class="dataTables_info">
                                        แสดง {{ $eventNews->firstItem() }} ถึง {{ $eventNews->lastItem() }}
                                        จากทั้งหมด {{ $eventNews->total() }} รายการ
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-7">
                                    <div class="dataTables_paginate paging_simple_numbers float-end">
                                        {{ $eventNews->links() }}
                                    </div>
                                </div>
                            </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Include Delete Confirmation Modal -->
@include('components.delete-confirmation-modal')

@endsection

