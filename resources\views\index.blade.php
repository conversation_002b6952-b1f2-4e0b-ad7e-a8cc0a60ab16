<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="🍽️ BBC HOME KITCHEN - ร้านอาหารตามสั่งออนไลน์ อาหารอร่อยส่งถึงบ้าน" />
    <meta name="author" content="BBC HOME KITCHEN" />
    <title>หน้าแรก - BBC HOME KITCHEN</title>
    <!-- Google Fonts - Prompt (Optimized) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link href="{{ asset('css/styles.css') }}" rel="stylesheet" />
    <!-- Restaurant Background CSS -->
    <link href="{{ asset('css/restaurant-background.css') }}" rel="stylesheet" />
    <!-- Performance Optimizations CSS -->
    <link href="{{ asset('css/performance-optimizations.css') }}" rel="stylesheet" />
    <!-- Prompt Font CSS -->
    <link href="{{ asset('css/prompt-font.css') }}" rel="stylesheet" />
    <style>
        /* Global Font Family - Prompt */
        * {
            font-family: 'Prompt', sans-serif !important;
        }

        body {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 400;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .navbar-brand {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        .nav-link {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .btn {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .card-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .section-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        .lead {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 400;
        }

        .display-3 {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        .dropdown-item {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 400;
        }

        .badge {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        /* Restaurant Theme Styles */

        .navbar {
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(8px);
            border-bottom: 2px solid rgba(255, 107, 107, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            position: relative;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #ff6b6b !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
            padding: 8px 16px !important;
            border-radius: 20px;
            transition: all 0.3s ease;
            margin: 0 4px;
        }

        .nav-link:hover {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .nav-link.active {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .navbar-toggler {
            border: 2px solid #ff6b6b;
            color: #ff6b6b;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
        }

        .hero-section {
            background: linear-gradient(135deg, #2d3436, #636e72);
            min-height: 70vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="20">🍽️</text></svg>') repeat;
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .section-title {
            color: #2d3436;
            font-weight: bold;
            margin-bottom: 2rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 2px;
        }

        .category-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .category-card:hover {
            background: linear-gradient(135deg, #0984e3, #74b9ff);
            transform: scale(1.05);
        }

        .footer-restaurant {
            background: linear-gradient(135deg, #2d3436, #636e72);
        }

        .btn-hero {
            background: linear-gradient(135deg, #e17055, #d63031);
            border: none;
            border-radius: 25px;
            padding: 1rem 2rem;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-hero:hover {
            background: linear-gradient(135deg, #d63031, #e17055);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(214, 48, 49, 0.4);
            color: white;
        }

        /* News Cards */
        .news-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .news-card .card-img-top {
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .news-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        /* Category Image Placeholders */
        .category-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
        }

        .category-image-placeholder {
            width: 100%;
            height: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-align: center;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .category-image-placeholder:hover {
            transform: scale(1.05);
        }

        .category-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .category-name {
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Category specific colors */
        

        /* Product Cards */
        .product-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .product-card .card-img-top {
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .bg-gradient-food {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .price-section {
            padding: 0.5rem;
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
            border-radius: 10px;
            margin: 0 1rem;
        }

        /* Carousel Controls */
        .carousel-control-prev,
        .carousel-control-next {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.9), rgba(238, 90, 36, 0.9));
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .carousel-control-prev {
            left: -30px;
        }

        .carousel-control-next {
            right: -30px;
        }

        .carousel-control-prev:hover,
        .carousel-control-next:hover {
            background: linear-gradient(135deg, rgba(255, 107, 107, 1), rgba(238, 90, 36, 1));
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            opacity: 1;
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        /* Carousel Indicators */
        .carousel-indicators {
            bottom: -50px;
        }

        .carousel-indicators [data-bs-target] {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: rgba(255, 107, 107, 0.5);
            border: 2px solid rgba(255, 107, 107, 0.8);
            transition: all 0.3s ease;
        }

        .carousel-indicators .active {
            background-color: #ff6b6b;
            transform: scale(1.2);
        }

        /* Auto-scroll animation - ปรับปรุงให้นุ่มนวลขึ้น */
        .carousel-item {
            transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* เพิ่มการปรับปรุงสำหรับ carousel inner */
        .carousel-inner {
            overflow: hidden;
            position: relative;
        }

        /* ปรับปรุงการเคลื่อนไหวของ carousel */
        .carousel-item.active,
        .carousel-item-next,
        .carousel-item-prev {
            display: block;
            transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* เพิ่ม GPU acceleration และปรับปรุงประสิทธิภาพ */
        .carousel-item {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
            will-change: transform, opacity;
        }

        /* ปรับปรุงการแสดงผลของ product cards */
        .product-card {
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        /* เพิ่มการปรับปรุงสำหรับ carousel controls */
        .carousel-control-prev,
        .carousel-control-next {
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        /* ปรับปรุงการแสดงผลของ indicators */
        .carousel-indicators [data-bs-target] {
            will-change: transform, background-color;
            transform: translateZ(0);
        }

        /* Mobile responsive controls */
        @media (max-width: 768px) {
            .carousel-control-prev,
            .carousel-control-next {
                width: 45px;
                height: 45px;
                left: 10px;
                right: 10px;
                opacity: 0.7;
            }

            .carousel-control-prev {
                left: 10px;
            }

            .carousel-control-next {
                right: 10px;
            }

            /* ปรับปรุงการแสดงผลบนมือถือ */
            .carousel-inner {
                touch-action: pan-y pinch-zoom;
            }

            .carousel-item {
                transition: transform 0.6s ease-out;
            }

            /* เพิ่มพื้นที่สำหรับ touch */
            .carousel-control-prev,
            .carousel-control-next {
                padding: 15px;
            }
        }

        /* สำหรับหน้าจอขนาดเล็กมาก */
        @media (max-width: 480px) {
            .carousel-control-prev,
            .carousel-control-next {
                width: 40px;
                height: 40px;
                opacity: 0.6;
            }

            .carousel-indicators {
                bottom: -40px;
            }

            .carousel-indicators [data-bs-target] {
                width: 10px;
                height: 10px;
            }
        }
    </style>
</head>
<body>
        <!-- Navigation-->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container px-4 px-lg-5">
                <a class="navbar-brand" href="{{route('index2')}}">🍽️ BBC HOME KITCHEN</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0 ms-lg-4">
                        <li class="nav-item"><a class="nav-link active" aria-current="page" href="{{route('index2')}}">หน้าแรก</a></li>
                        <li class="nav-item"><a class="nav-link" href="/event_new">ข่าวประชาสัมพันธ์</a></li>                                                             
                        <li class="nav-item"><a class="nav-link" href="{{ route('reviews.index') }}">รีวิว</a></li>
                        <li class="nav-item"><a class="nav-link" href="{{ route('menu.cards') }}">เมนูอาหาร</a></li>
                        <li class="nav-item"><a class="nav-link" href="/about">เกี่ยวกับเรา</a></li>
                    </ul>

                    <!-- Login/Register buttons on the right -->
                    <div class="d-flex">
                    @auth
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>{{ Auth::user()->name }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('customer.profile') }}">
                                    <i class="bi bi-person me-2"></i>โปรไฟล์ของฉัน
                                </a></li>
                                @if(Auth::user()->isAdmin())
                                    <li><a class="dropdown-item" href="{{ route('dashboard') }}">
                                        <i class="bi bi-speedometer2 me-2"></i>แดชบอร์ด
                                    </a></li>
                                @endif
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="bi bi-box-arrow-right me-2"></i>ออกจากระบบ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @else
                        <a href="{{route('login')}}" class="btn btn-outline-light me-2">
                            <i class="bi bi-box-arrow-in-right me-1"></i>เข้าสู่ระบบ
                        </a>
                        <a href="{{route('register')}}" class="btn btn-light">
                            <i class="bi bi-person-plus me-1"></i>สมัครสมาชิก
                        </a>
                    @endauth
                </div>
                    
                   
                        
                </div>
            </div>
                        
        </nav>
        <!-- Hero Section-->
        <section class="hero-section">
            <div class="container px-4 px-lg-5">
                <div class="hero-content text-center text-white">
                    <h1 class="display-3 fw-bolder mb-4">🍽️ BBC HOME KITCHEN</h1>
                    <p class="lead fs-4 mb-4">ร้านอาหารตามสั่ง</p>
                    <p class="fs-5 mb-5">อาหารอร่อย ปรุงสดใหม่ทุกวัน</p>
                    <a href="{{ route('menu.cards') }}" class="btn btn-hero btn-lg">
                        <i class="bi bi-arrow-right"></i> ดูเมนูอาหาร
                    </a>
                </div>
            </div>
        </section>
        <!-- Categories Section -->
    <!--    <section class="py-5 bg-light">
            <div class="container px-4 px-lg-5">
                <div class="text-center mb-5">
                    <h2 class="section-title">🍽️ หมวดหมู่อาหาร</h2>
                    <p class="lead fw-normal text-muted mb-0">เลือกหมวดหมู่อาหารที่คุณต้องการ</p>
                </div>
                <div class="row gx-4 gx-lg-5 row-cols-2 row-cols-md-3 row-cols-lg-5 justify-content-center">
                    @forelse ($categories as $category)
                        <div class="col mb-4">
                            <div class="card category-card h-100 text-center border-0">
                                @if($category->image_path)
                                    <div class="category-image-container">
                                        <div class="category-image-placeholder {{ str_replace([' ', 'และ'], ['-', ''], strtolower($category->name)) }}">
                                            <div class="category-icon">
                                                @if($category->name == 'อาหารไทย') 🍛
                                                @elseif($category->name == 'อาหารจีน') 🥟
                                                @elseif($category->name == 'อาหารญี่ปุ่น') 🍣
                                                @elseif($category->name == 'อาหารเกาหลี') 🍜
                                                @elseif($category->name == 'อาหารอิตาเลียน') 🍕
                                                @elseif($category->name == 'อาหารฟาสต์ฟู้ด') 🍔
                                                @elseif($category->name == 'เครื่องดื่มร้อน') ☕
                                                @elseif($category->name == 'เครื่องดื่มเย็น') 🥤
                                                @elseif($category->name == 'ของหวาน') 🍰
                                                @elseif($category->name == 'สลัดและอาหารเพื่อสุขภาพ') 🥗
                                                @else 🍽️
                                                @endif
                                            </div>
                                            <div class="category-name">{{ $category->name }}</div>
                                        </div>
                                    </div>
                                @else
                                    <div class="category-image-placeholder default-category">
                                        <div class="category-icon">🍽️</div>
                                        <div class="category-name">{{ $category->name }}</div>
                                    </div>
                                @endif
                                <div class="card-body p-3">
                                    <a href="{{ route('shop.category', $category->id) }}" class="btn btn-primary btn-sm w-100">
                                        <i class="bi bi-arrow-right me-1"></i>ดูเมนู
                                    </a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12 text-center">
                            <p class="text-muted">ยังไม่มีหมวดหมู่อาหาร</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </section> -->



        <!-- Featured Products Section -->
        <section class="py-5">
            <div class="container px-4 px-lg-5">
                <div class="text-center mb-5">
                    <h2 class="section-title">🍽️ เมนูแนะนำ</h2>
                    <p class="lead fw-normal text-muted mb-3">เมนูยอดนิยมและเมนูใหม่ล่าสุดจากครัวของเรา</p>
                <!--    <button class="btn btn-outline-secondary btn-sm" onclick="refreshFeaturedProducts()" title="สุ่มเมนูใหม่">
                        <i class="bi bi-arrow-clockwise me-1"></i>สุ่มเมนูใหม่
                    </button> -->
                </div>

                <!-- Products Carousel -->
                <div id="productsCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
                    <!-- Carousel Indicators -->
                    @if($featuredProducts->chunk(4)->count() > 1)
                        <div class="carousel-indicators">
                            @foreach($featuredProducts->chunk(4) as $index => $productChunk)
                                <button type="button" data-bs-target="#productsCarousel" data-bs-slide-to="{{ $index }}"
                                        class="{{ $index == 0 ? 'active' : '' }}"
                                        aria-current="{{ $index == 0 ? 'true' : 'false' }}"
                                        aria-label="Slide {{ $index + 1 }}"></button>
                            @endforeach
                        </div>
                    @endif

                    <div class="carousel-inner">
                        @foreach($featuredProducts->chunk(4) as $index => $productChunk)
                            <div class="carousel-item {{ $index == 0 ? 'active' : '' }}">
                                <div class="row gx-4 gx-lg-5 row-cols-1 row-cols-md-2 row-cols-xl-4 justify-content-center">
                                    @foreach($productChunk as $product)
                                        <div class="col mb-5">
                                            <div class="card product-card h-100">
                                                @if($product->picture && $product->picture !== 'nopic.png' && file_exists(public_path('storage/products/' . $product->picture)))
                                                    <img class="card-img-top" src="{{ asset('storage/products/' . $product->picture) }}" alt="{{ $product->proname }}" style="height: 200px; object-fit: cover;" />
                                                @else
                                                    <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 200px; background: linear-gradient(135deg, #ff9500, #ff6b00);">
                                                        <div class="text-center text-white">
                                                            <div style="font-size: 3rem; margin-bottom: 10px;">🍽️</div>
                                                            <div style="font-size: 0.9rem; font-weight: bold;">{{ $product->proname }}</div>
                                                        </div>
                                                    </div>
                                                @endif
                                                <div class="card-body p-4">
                                                    <div class="text-center">
                                                        <p class="fw-bolder mb-2">{{ $product->proname }}</p>
                                                        <p class="text-muted small mb-3">{{ Str::limit($product->prodetail, 60) }}</p>
                                                        <div class="d-flex justify-content-center align-items-center mb-3">
                                                            <span class="badge bg-primary me-2">{{ $product->category->name ?? 'อาหาร' }}</span>
                                                        <!--    <span class="text-muted small">{{ $product->brand->brand_name ?? 'BBC' }}</span> -->
                                                        </div>
                                                        <div class="">
                                                            <span class="h5 text-primary fw-bold">฿{{ number_format($product->price, 0) }}</span>
                                                        </div>
                                                        @if($product->review_count > 0)
                                                            <div class="d-flex justify-content-center small text-warning mb-2">
                                                                @for($i = 1; $i <= 5; $i++)
                                                                    @if($i <= $product->average_rating)
                                                                        <div class="bi-star-fill"></div>
                                                                    @else
                                                                        <div class="bi-star"></div>
                                                                    @endif
                                                                @endfor
                                                                <span class="text-muted ms-1">({{ $product->review_count }})</span>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="card-footer p-4 pt-0 border-top-0 bg-transparent">
                                                    <div class="text-center">
                                                        <a class="btn btn-primary" href="{{ route('shop.show', $product->id) }}">
                                                            <i class="bi bi-eye me-1"></i>ดูรายละเอียด
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Carousel Controls -->
                    @if($featuredProducts->chunk(4)->count() > 1)
                        <button class="carousel-control-prev" type="button" data-bs-target="#productsCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">เมนูก่อนหน้า</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#productsCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">เมนูถัดไป</span>
                        </button>
                    @endif
                </div>

                <!-- Carousel Controls Info -->
               
                <div class="text-center mt-4">
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center align-items-center">
                        
                        @if($featuredProducts->chunk(4)->count() > 1)
                            
                        @endif
                    </div>
                </div>
            </div>
        </section>

        <!-- News Section -->
        <section class="py-5 bg-light">
            <div class="container px-4 px-lg-5">
                <div class="text-center mb-5">
                    <h2 class="section-title"> ข่าวประชาสัมพันธ์</h2>
                    <p class="lead fw-normal text-muted mb-0">โปรโมชั่นและข่าวสารล่าสุดจากเรา</p>
                </div>
                <div class="row gx-4 gx-lg-5 row-cols-1 row-cols-md-2 row-cols-xl-3 justify-content-center">
                    @foreach($eventNews as $news)
                        <div class="col mb-5">
                            <div class="card h-100 news-card">
                                @if($news->pic)
                                    <img class="card-img-top lazy-image" data-src="{{ asset('storage/event_news/' . $news->pic) }}" alt="{{ $news->title }}" loading="lazy" />
                                @else
                                    <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="bi bi-megaphone text-white" style="font-size: 3rem;"></i>
                                    </div>
                                @endif
                                <div class="card-body p-4">
                                    <div class="text-center">
                                        <h5 class="fw-bolder mb-3">{{ $news->title }}</h5>
                                        <p class="text-muted small mb-3">
                                            <i class="bi bi-calendar-event me-1"></i>
                                            {{ \Carbon\Carbon::parse($news->event_date)->format('d/m/Y') }}
                                        </p>
                                        <p class="card-text">{{ Str::limit($news->description, 100) }}</p>
                                    </div>
                                </div>
                                <div class="card-footer p-4 pt-0 border-top-0 bg-transparent">
                                    <div class="text-center">
                                        <a class="btn btn-primary" href="{{ route('event_new.index') }}">
                                            <i class="bi bi-arrow-right me-1"></i>อ่านเพิ่มเติม
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="text-center mt-4">
                    <a href="{{ route('event_new.index') }}" class="btn btn-outline-primary btn-lg">
                        <i class="bi bi-newspaper me-2"></i>ดูข่าวทั้งหมด
                    </a>
                </div>
            </div>
        </section>

        <!-- Footer-->
        <footer class="py-5 footer-restaurant">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-white mb-3">🍽️ BBC HOME KITCHEN</h5>
                        <p class="text-white-50">ร้านอาหารตามสั่งออนไลน์ อาหารอร่อยส่งถึงบ้าน ปรุงสดใหม่ทุกวัน</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="text-white-50 mb-0">&copy; 2024 BBC HOME KITCHEN</p>
                        <p class="text-white-50">ร้านอาหารตามสั่งออนไลน์</p>
                    </div>
                </div>
            </div>
        </footer>
        <!-- Bootstrap core JS-->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
        <!-- Core theme JS-->
        <script src="js/scripts.js"></script>
        <!-- Lazy Loading JS-->
        <script src="{{ asset('js/lazy-loading.js') }}"></script>
        <!-- Restaurant Effects JS-->
        <script src="{{ asset('js/restaurant-effects.js') }}"></script>

        <!-- Custom JS for Featured Products Carousel -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // เริ่มต้น carousel อัตโนมัติ พร้อมการปรับปรุงความนุ่มนวล
                const carouselElement = document.querySelector('#productsCarousel');

                if (carouselElement) {
                    const carousel = new bootstrap.Carousel(carouselElement, {
                        interval: 6000,    // เปลี่ยนทุก 6 วินาที (ช้าลงเล็กน้อย)
                        wrap: true,        // วนกลับไปหน้าแรกเมื่อถึงหน้าสุดท้าย
                        pause: 'hover',    // หยุดเมื่อ hover
                        touch: true,       // รองรับการสัมผัส
                        keyboard: true     // รองรับคีย์บอร์ด
                    });

                    // ปรับปรุงเอฟเฟกต์การเปลี่ยนสไลด์ให้นุ่มนวลขึ้น
                    let isTransitioning = false;

                    carouselElement.addEventListener('slide.bs.carousel', function (e) {
                        if (isTransitioning) return;
                        isTransitioning = true;

                        // เพิ่มเอฟเฟกต์ fade แบบนุ่มนวล
                        const activeSlide = carouselElement.querySelector('.carousel-item.active');
                        const nextSlide = e.relatedTarget;

                        if (activeSlide && nextSlide) {
                            // เตรียม next slide
                            nextSlide.style.opacity = '0';
                            nextSlide.style.transform = 'translateX(0)';

                            // Fade out current slide
                            activeSlide.style.transition = 'opacity 0.4s ease-out';
                            activeSlide.style.opacity = '0.3';
                        }
                    });

                    carouselElement.addEventListener('slid.bs.carousel', function (e) {
                        // คืนค่าให้สไลด์ใหม่
                        const activeSlide = carouselElement.querySelector('.carousel-item.active');
                        if (activeSlide) {
                            activeSlide.style.transition = 'opacity 0.4s ease-in';
                            activeSlide.style.opacity = '1';
                            activeSlide.style.transform = 'translateX(0)';
                        }

                        // รีเซ็ต opacity ของสไลด์อื่นๆ
                        const allSlides = carouselElement.querySelectorAll('.carousel-item');
                        allSlides.forEach(slide => {
                            if (!slide.classList.contains('active')) {
                                slide.style.opacity = '1';
                                slide.style.transition = '';
                            }
                        });

                        isTransitioning = false;
                    });

                    // เพิ่มการป้องกันการคลิกซ้ำเร็วเกินไป
                    const prevBtn = carouselElement.querySelector('.carousel-control-prev');
                    const nextBtn = carouselElement.querySelector('.carousel-control-next');

                    if (prevBtn && nextBtn) {
                        let clickTimeout;

                        [prevBtn, nextBtn].forEach(btn => {
                            btn.addEventListener('click', function(e) {
                                if (isTransitioning) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    return false;
                                }

                                // ป้องกันการคลิกซ้ำเร็วเกินไป
                                clearTimeout(clickTimeout);
                                clickTimeout = setTimeout(() => {
                                    isTransitioning = false;
                                }, 800);
                            });
                        });
                    }
                }

                // เพิ่มการแสดงผลแบบ smooth สำหรับ product cards
                const productCards = document.querySelectorAll('.product-card');
                productCards.forEach((card, index) => {
                    card.style.animationDelay = `${index * 0.1}s`;
                    card.classList.add('fade-in-up');
                });
            });

            // ฟังก์ชันสำหรับรีเฟรชเมนูแนะนำ (สุ่มใหม่)
            function refreshFeaturedProducts() {
                // แสดง loading indicator
                const carousel = document.querySelector('#productsCarousel');
                const refreshBtn = document.querySelector('button[onclick="refreshFeaturedProducts()"]');

                if (carousel && refreshBtn) {
                    // เปลี่ยนปุ่มเป็น loading state
                    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2 spinner-border spinner-border-sm"></i>กำลังสุ่ม...';
                    refreshBtn.disabled = true;

                    // เพิ่มเอฟเฟกต์ fade
                    carousel.style.transition = 'opacity 0.5s ease';
                    carousel.style.opacity = '0.3';

                    // รีโหลดหน้าเพื่อให้ได้เมนูสุ่มใหม่
                    setTimeout(() => {
                        window.location.reload();
                    }, 800);
                }
            }

            // เพิ่มการควบคุม carousel ด้วยคีย์บอร์ด (ปรับปรุงแล้ว)
            document.addEventListener('keydown', function(e) {
                const carousel = document.querySelector('#productsCarousel');
                if (carousel && !isTransitioning) {
                    // ตรวจสอบว่าไม่ได้อยู่ใน input field
                    if (document.activeElement.tagName !== 'INPUT' &&
                        document.activeElement.tagName !== 'TEXTAREA') {

                        if (e.key === 'ArrowLeft') {
                            e.preventDefault();
                            const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                            if (carouselInstance) {
                                carouselInstance.prev();
                            }
                        } else if (e.key === 'ArrowRight') {
                            e.preventDefault();
                            const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                            if (carouselInstance) {
                                carouselInstance.next();
                            }
                        }
                    }
                }
            });

            // เพิ่มการหยุด auto-slide เมื่อ hover (ปรับปรุงแล้ว)
            if (carouselElement) {
                let hoverTimeout;

                carouselElement.addEventListener('mouseenter', function() {
                    clearTimeout(hoverTimeout);
                    const carouselInstance = bootstrap.Carousel.getInstance(carouselElement);
                    if (carouselInstance && !isTransitioning) {
                        carouselInstance.pause();
                    }
                });

                carouselElement.addEventListener('mouseleave', function() {
                    clearTimeout(hoverTimeout);
                    hoverTimeout = setTimeout(() => {
                        const carouselInstance = bootstrap.Carousel.getInstance(carouselElement);
                        if (carouselInstance && !isTransitioning) {
                            carouselInstance.cycle();
                        }
                    }, 300); // รอ 300ms ก่อนเริ่ม auto-slide อีกครั้ง
                });

                // เพิ่มการจัดการเมื่อ focus/blur สำหรับ accessibility
                carouselElement.addEventListener('focusin', function() {
                    const carouselInstance = bootstrap.Carousel.getInstance(carouselElement);
                    if (carouselInstance) {
                        carouselInstance.pause();
                    }
                });

                carouselElement.addEventListener('focusout', function() {
                    setTimeout(() => {
                        if (!carouselElement.contains(document.activeElement)) {
                            const carouselInstance = bootstrap.Carousel.getInstance(carouselElement);
                            if (carouselInstance) {
                                carouselInstance.cycle();
                            }
                        }
                    }, 100);
                });

                // เพิ่ม Touch/Swipe Support สำหรับมือถือ
                let touchStartX = 0;
                let touchEndX = 0;
                let touchStartY = 0;
                let touchEndY = 0;

                carouselElement.addEventListener('touchstart', function(e) {
                    touchStartX = e.changedTouches[0].screenX;
                    touchStartY = e.changedTouches[0].screenY;
                }, { passive: true });

                carouselElement.addEventListener('touchend', function(e) {
                    touchEndX = e.changedTouches[0].screenX;
                    touchEndY = e.changedTouches[0].screenY;

                    const deltaX = touchEndX - touchStartX;
                    const deltaY = touchEndY - touchStartY;

                    // ตรวจสอบว่าเป็นการ swipe แนวนอนและไม่ใช่แนวตั้ง
                    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                        if (!isTransitioning) {
                            const carouselInstance = bootstrap.Carousel.getInstance(carouselElement);
                            if (carouselInstance) {
                                if (deltaX > 0) {
                                    carouselInstance.prev(); // Swipe right = previous
                                } else {
                                    carouselInstance.next(); // Swipe left = next
                                }
                            }
                        }
                    }
                }, { passive: true });

                // เพิ่มการแสดง loading state เมื่อเปลี่ยนสไลด์
                carouselElement.addEventListener('slide.bs.carousel', function() {
                    carouselElement.style.pointerEvents = 'none';
                });

                carouselElement.addEventListener('slid.bs.carousel', function() {
                    setTimeout(() => {
                        carouselElement.style.pointerEvents = 'auto';
                    }, 100);
                });
            }
        </script>

        <!-- Optimized CSS for performance -->
        <style>
            .fade-in-up {
                animation: fadeInUp 0.3s ease-out forwards;
                opacity: 0;
                transform: translateY(10px);
            }

            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .carousel-item {
                transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.4s ease;
                will-change: transform, opacity;
            }

            .product-card {
                transition: all 0.3s ease;
                animation: fadeInUp 0.6s ease forwards;
                opacity: 0;
                transform: translateY(20px);
            }

            .product-card:hover {
                transform: translateY(-8px);
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.2);
            }

            /* Fade in animation */
            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* Stagger animation for cards */
            .product-card:nth-child(1) { animation-delay: 0.1s; }
            .product-card:nth-child(2) { animation-delay: 0.2s; }
            .product-card:nth-child(3) { animation-delay: 0.3s; }
            .product-card:nth-child(4) { animation-delay: 0.4s; }

            /* Loading spinner */
            .spinner-border-sm {
                width: 1rem;
                height: 1rem;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* Smooth carousel transition - ปรับปรุงแล้ว */
            .carousel-inner {
                overflow: hidden;
                position: relative;
                transform: translateZ(0);
            }

            .carousel-item.active,
            .carousel-item-next,
            .carousel-item-prev {
                display: block;
                will-change: transform;
            }

            /* เพิ่มการปรับปรุงสำหรับ mobile */
            @media (max-width: 768px) {
                .carousel-item {
                    transition: transform 0.6s ease-out, opacity 0.3s ease;
                }
            }

            /* Touch/swipe support indicators */
            .carousel-touch-indicator {
                position: absolute;
                bottom: -80px;
                left: 50%;
                transform: translateX(-50%);
                color: #6c757d;
                font-size: 0.8rem;
            }
        </style>
    </body>
</html>
