<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\EventNews;

try {
    echo "🖼️ ทดสอบการแสดงรูปภาพทั้งหมด...\n\n";
    
    // ทดสอบรูปภาพสินค้า
    echo "📦 ทดสอบรูปภาพสินค้า:\n";
    $products = Product::whereNotNull('picture')->take(5)->get();
    
    foreach($products as $product) {
        $storagePath = storage_path('app/public/products/' . $product->picture);
        $publicPath = public_path('storage/products/' . $product->picture);
        
        echo "  📝 {$product->proname}\n";
        echo "     🖼️ ไฟล์: {$product->picture}\n";
        echo "     📁 Storage: " . (file_exists($storagePath) ? '✅' : '❌') . "\n";
        echo "     🌐 Public: " . (file_exists($publicPath) ? '✅' : '❌') . "\n";
        echo "     🔗 URL: " . asset('storage/products/' . $product->picture) . "\n\n";
    }
    
    // ทดสอบรูปภาพหมวดหมู่
    echo "📂 ทดสอบรูปภาพหมวดหมู่:\n";
    $categories = Category::whereNotNull('image_path')->take(5)->get();
    
    foreach($categories as $category) {
        $storagePath = storage_path('app/public/categories/' . $category->image_path);
        $publicPath = public_path('storage/categories/' . $category->image_path);
        
        echo "  📝 {$category->name}\n";
        echo "     🖼️ ไฟล์: {$category->image_path}\n";
        echo "     📁 Storage: " . (file_exists($storagePath) ? '✅' : '❌') . "\n";
        echo "     🌐 Public: " . (file_exists($publicPath) ? '✅' : '❌') . "\n";
        echo "     🔗 URL: " . asset('storage/categories/' . $category->image_path) . "\n\n";
    }
    
    // ทดสอบรูปภาพแบรนด์
    echo "🏷️ ทดสอบรูปภาพแบรนด์:\n";
    $brands = Brand::whereNotNull('brand_image')->take(5)->get();
    
    foreach($brands as $brand) {
        $storagePath = storage_path('app/public/brands/' . $brand->brand_image);
        $publicPath = public_path('storage/brands/' . $brand->brand_image);
        
        echo "  📝 {$brand->brand_name}\n";
        echo "     🖼️ ไฟล์: {$brand->brand_image}\n";
        echo "     📁 Storage: " . (file_exists($storagePath) ? '✅' : '❌') . "\n";
        echo "     🌐 Public: " . (file_exists($publicPath) ? '✅' : '❌') . "\n";
        echo "     🔗 URL: " . asset('storage/brands/' . $brand->brand_image) . "\n\n";
    }
    
    // ทดสอบรูปภาพข่าวสาร
    echo "📰 ทดสอบรูปภาพข่าวสาร:\n";
    $news = EventNews::whereNotNull('pic')->take(5)->get();
    
    foreach($news as $item) {
        $storagePath = storage_path('app/public/event_news/' . $item->pic);
        $publicPath = public_path('storage/event_news/' . $item->pic);
        
        echo "  📝 {$item->title}\n";
        echo "     🖼️ ไฟล์: {$item->pic}\n";
        echo "     📁 Storage: " . (file_exists($storagePath) ? '✅' : '❌') . "\n";
        echo "     🌐 Public: " . (file_exists($publicPath) ? '✅' : '❌') . "\n";
        echo "     🔗 URL: " . asset('storage/event_news/' . $item->pic) . "\n\n";
    }
    
    // สรุปผลการทดสอบ
    echo "📊 สรุปผลการทดสอบ:\n";
    
    $totalProducts = Product::whereNotNull('picture')->count();
    $workingProducts = 0;
    foreach(Product::whereNotNull('picture')->get() as $p) {
        if(file_exists(public_path('storage/products/' . $p->picture))) $workingProducts++;
    }
    
    $totalCategories = Category::whereNotNull('image_path')->count();
    $workingCategories = 0;
    foreach(Category::whereNotNull('image_path')->get() as $c) {
        if(file_exists(public_path('storage/categories/' . $c->image_path))) $workingCategories++;
    }
    
    $totalBrands = Brand::whereNotNull('brand_image')->count();
    $workingBrands = 0;
    foreach(Brand::whereNotNull('brand_image')->get() as $b) {
        if(file_exists(public_path('storage/brands/' . $b->brand_image))) $workingBrands++;
    }
    
    $totalNews = EventNews::whereNotNull('pic')->count();
    $workingNews = 0;
    foreach(EventNews::whereNotNull('pic')->get() as $n) {
        if(file_exists(public_path('storage/event_news/' . $n->pic))) $workingNews++;
    }
    
    echo "  📦 สินค้า: {$workingProducts}/{$totalProducts} รูป (" . ($totalProducts > 0 ? round(($workingProducts/$totalProducts)*100, 1) : 0) . "%)\n";
    echo "  📂 หมวดหมู่: {$workingCategories}/{$totalCategories} รูป (" . ($totalCategories > 0 ? round(($workingCategories/$totalCategories)*100, 1) : 0) . "%)\n";
    echo "  🏷️ แบรนด์: {$workingBrands}/{$totalBrands} รูป (" . ($totalBrands > 0 ? round(($workingBrands/$totalBrands)*100, 1) : 0) . "%)\n";
    echo "  📰 ข่าวสาร: {$workingNews}/{$totalNews} รูป (" . ($totalNews > 0 ? round(($workingNews/$totalNews)*100, 1) : 0) . "%)\n\n";
    
    $totalImages = $totalProducts + $totalCategories + $totalBrands + $totalNews;
    $workingImages = $workingProducts + $workingCategories + $workingBrands + $workingNews;
    
    if($workingImages == $totalImages) {
        echo "✅ รูปภาพทั้งหมดแสดงได้ปกติ! ({$workingImages}/{$totalImages})\n";
    } else {
        echo "⚠️ มีรูปภาพบางส่วนที่ไม่แสดง ({$workingImages}/{$totalImages})\n";
    }
    
    echo "\n🌐 URL ทดสอบ:\n";
    echo "  🏠 หน้าแรก: http://127.0.0.1:8000\n";
    echo "  🛒 ร้านค้า: http://127.0.0.1:8000/shop\n";
    echo "  📰 ข่าวสาร: http://127.0.0.1:8000/news\n";
    echo "  🏢 แอดมิน: http://127.0.0.1:8000/admin/dashboard\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
