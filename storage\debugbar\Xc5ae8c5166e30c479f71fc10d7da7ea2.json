{"__meta": {"id": "Xc5ae8c5166e30c479f71fc10d7da7ea2", "datetime": "2025-09-25 20:29:28", "utime": **********.64067, "method": "GET", "uri": "/u1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.382872, "end": **********.640699, "duration": 0.2578268051147461, "duration_str": "258ms", "measures": [{"label": "Booting", "start": **********.382872, "relative_start": 0, "end": **********.550356, "relative_end": **********.550356, "duration": 0.16748380661010742, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.550372, "relative_start": 0.16749978065490723, "end": **********.640702, "relative_end": 3.0994415283203125e-06, "duration": 0.09033012390136719, "duration_str": "90.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20884960, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "admin.user", "param_count": null, "params": [], "start": **********.619955, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/user.blade.phpadmin.user", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}}, {"name": "admin.admin_master", "param_count": null, "params": [], "start": **********.627022, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/admin_master.blade.phpadmin.admin_master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fadmin_master.blade.php&line=1", "ajax": false, "filename": "admin_master.blade.php", "line": "?"}}, {"name": "sweetalert::alert", "param_count": null, "params": [], "start": **********.627874, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}}, {"name": "admin.body.header", "param_count": null, "params": [], "start": **********.628512, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.phpadmin.body.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "admin.body.slidemenu", "param_count": null, "params": [], "start": **********.632078, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/slidemenu.blade.phpadmin.body.slidemenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fslidemenu.blade.php&line=1", "ajax": false, "filename": "slidemenu.blade.php", "line": "?"}}, {"name": "admin.body.footer", "param_count": null, "params": [], "start": **********.632763, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/footer.blade.phpadmin.body.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET u1", "middleware": "web, auth, verified, admin", "uses": "Closure() {#250\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#235 …}\n  file: \"C:\\xampp\\htdocs\\shopping67\\routes\\web.php\"\n  line: \"64 to 66\"\n}", "namespace": null, "prefix": "", "where": [], "as": "user1", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Froutes%2Fweb.php&line=64\" onclick=\"\">routes/web.php:64-66</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00334, "accumulated_duration_str": "3.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.590793, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.601794, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 0, "width_percent": 64.671}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "admin.user", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/user.blade.php", "line": 26}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.623533, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "admin.user:26", "source": "view::admin.user:26", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fuser.blade.php&line=26", "ajax": false, "filename": "user.blade.php", "line": "26"}, "connection": "shopping67", "start_percent": 64.671, "width_percent": 17.365}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "admin.body.header", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.629058, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "admin.body.header:32", "source": "view::admin.body.header:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=32", "ajax": false, "filename": "header.blade.php", "line": "32"}, "connection": "shopping67", "start_percent": 82.036, "width_percent": 17.964}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/u1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/u1", "status_code": "<pre class=sf-dump id=sf-dump-2004976285 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2004976285\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1334720375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1334720375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-575807821 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575807821\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-529414255 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/brand/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlpMbk5pTlZMcTNjZjRRYXpEa244TlE9PSIsInZhbHVlIjoiVnpkT252dFpUbTl2a2pXOGVZbFkyV0sxNlYyTTVXdmRWUzE1N1N4RzFDUUV2d2tUdm5OK2xkWXZyZDlFVitnM2o0a3hyRXExQnFsVUtVU2RvaWg2MXJTcHFRczg1bitMVFRqelhwYmx1YzZuZGZkZlpBQ3FmU2M0VmFRRFdVQTUiLCJtYWMiOiI4OTRiNzZiM2IzYmY0YjAyZDgyZDM3Y2I1YmMxYjlhNWUzZGZmZWI0M2I2MGI2NDBmNDFmMTcxYWY5M2Y5NTBkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRSYnJic0dqeVVyYWd0SCtVWmcxRnc9PSIsInZhbHVlIjoiRDdWbkF1YXZWc1FmQlBRdEYyMTAzM3BadFBLWHhldGRaRHV1Z2VpbHRZTGFRMlNoeUJsbkw3SCsybWMxbFI3bFphSlI2ZEZSUjFIUXhQZDFiK05UbFpyUGdTdkxkdkUvZWZjbHgvNkE5cTdmSllSUzhWYjM3QkFQRTFPYzMxQ2kiLCJtYWMiOiIyMWM2ZDk0ZDA0MDAyNzUzZGYxNWY5NDg5ZDc2ZDg5MDY0ODQ4ODgwODVkZDdjN2Q2ZWI2ZmUzNDUwMmM4YjQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529414255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-342199369 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WMTIWfNahNQIltL7Sh8nxhpUhpWn05qNcCODNhVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342199369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1089800299 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 13:29:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImwzU0NmU2tuazlyOXJxeEc3Y1dJdlE9PSIsInZhbHVlIjoiRWJwSHhTbTlzVjRIOTk4NTdGaVVIMWVEakFBd2k2QUFSQ1FLYy9FbENVT0hOTmJ6K1ZQR2lKVThuSytFbldiaUlOdllOamNaTDlXUUh3WWxQR2o4NytYZGJFK00rMnJZR1FTbHFveEZabm5ha3ZvTkNMUUJwTUk1d3drY1ZBZzQiLCJtYWMiOiJhMjZkZGY1M2ZmMjI1NTEyMmU2MWQ1MzcwYTM1MDk5MTc1MzUwYmRhY2E1OThiNjc4MjI1NjhhOGU1NmU5YzFmIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:29:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InVsaFhZVmdtOEJtYTI2MENOS1c1eHc9PSIsInZhbHVlIjoiNVhrVlVDdUNKc0dyUFh0akVDa29mOXoxZXIvbGZVUTBPUXY4SXV4bWY3Z1NsOTZub3VNN2hhWHZGM3k2UlNIUzBSOHBkLzlMYnpOMUx4VXN0U3g0ckZ0eFdNWUVhVFpPYzNCdHVmTXpFY1RLZDJnSDg2MldoYU9RWERiaUs5T1AiLCJtYWMiOiJlMWUzYjViYTlmYzliOGFjZjU4NWM1ZjY2MjJhMDcwMjc2MDYzNjljZjFiNzE4OWFiYzUzYThhM2I1YjNlYzdhIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:29:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImwzU0NmU2tuazlyOXJxeEc3Y1dJdlE9PSIsInZhbHVlIjoiRWJwSHhTbTlzVjRIOTk4NTdGaVVIMWVEakFBd2k2QUFSQ1FLYy9FbENVT0hOTmJ6K1ZQR2lKVThuSytFbldiaUlOdllOamNaTDlXUUh3WWxQR2o4NytYZGJFK00rMnJZR1FTbHFveEZabm5ha3ZvTkNMUUJwTUk1d3drY1ZBZzQiLCJtYWMiOiJhMjZkZGY1M2ZmMjI1NTEyMmU2MWQ1MzcwYTM1MDk5MTc1MzUwYmRhY2E1OThiNjc4MjI1NjhhOGU1NmU5YzFmIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:29:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InVsaFhZVmdtOEJtYTI2MENOS1c1eHc9PSIsInZhbHVlIjoiNVhrVlVDdUNKc0dyUFh0akVDa29mOXoxZXIvbGZVUTBPUXY4SXV4bWY3Z1NsOTZub3VNN2hhWHZGM3k2UlNIUzBSOHBkLzlMYnpOMUx4VXN0U3g0ckZ0eFdNWUVhVFpPYzNCdHVmTXpFY1RLZDJnSDg2MldoYU9RWERiaUs5T1AiLCJtYWMiOiJlMWUzYjViYTlmYzliOGFjZjU4NWM1ZjY2MjJhMDcwMjc2MDYzNjljZjFiNzE4OWFiYzUzYThhM2I1YjNlYzdhIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:29:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1089800299\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1523911817 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://127.0.0.1:8000/u1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523911817\", {\"maxDepth\":0})</script>\n"}}