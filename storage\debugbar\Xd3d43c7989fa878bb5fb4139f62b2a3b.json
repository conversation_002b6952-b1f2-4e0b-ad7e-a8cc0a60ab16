{"__meta": {"id": "Xd3d43c7989fa878bb5fb4139f62b2a3b", "datetime": "2025-09-25 19:49:26", "utime": 1758804566.09377, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758804565.614567, "end": 1758804566.093795, "duration": 0.47922801971435547, "duration_str": "479ms", "measures": [{"label": "Booting", "start": 1758804565.614567, "relative_start": 0, "end": 1758804565.741506, "relative_end": 1758804565.741506, "duration": 0.126939058303833, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1758804565.741517, "relative_start": 0.12695002555847168, "end": 1758804566.093798, "relative_end": 2.86102294921875e-06, "duration": 0.352280855178833, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 18829360, "peak_usage_str": "18MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1758804565.834276, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "layouts.restaurant", "param_count": null, "params": [], "start": 1758804566.078746, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/layouts/restaurant.blade.phplayouts.restaurant", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Flayouts%2Frestaurant.blade.php&line=1", "ajax": false, "filename": "restaurant.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest, Closure", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=18\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2051742279 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2051742279\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1183708473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1183708473\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1741349865 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1741349865\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1544564693 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/about</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IllFS2MzSzFqMTlvdmd1b2J0S3pJSlE9PSIsInZhbHVlIjoiMG5WVEI4RVlRc01LbnIyekZvaGFVT1g5UzBRRm5pcHhrUzdOV0w1NG5GeVRhd0c1cnVFQ3ZUSk9zN2tLOUtNS0lqV2RSK2l3eTU3bFFVSitBVyt6cDhLM0c0bHVYY1JWNmxYb3ZMY3oxVHlna2xkcm5CblZvL3hUZVpDWG5ZTjkiLCJtYWMiOiI0MjcxZjRjNTViNGMwYzc5NjI2MDFjODE2YWE2Nzg3NmFjYmM3MjA0MDc2M2NjMDU0MzYwMGY0ZDZjNGVhOGVjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImxMVlpwanlxTFBrWEs3MHVSVzlqd0E9PSIsInZhbHVlIjoibXN0WEZBTGx3R001VUFLSTBlYVZkMHdIYUkyc2d5RmluQW5MOTJnczVNUjFMNmNOOG9TTFJvNldKT3g2UHdXODdjYi9VVDRxU29VK0VSZml0bHIvYmUyRU9kTmVJKzRUM25aMldkMDNXNlVoMzlIdEZPaEtQRzBmd2RKWVhhcWMiLCJtYWMiOiI5ZTgwMDM2MjJlYjkwMDU4MmZiNzFlZmY4NTRiNDUyMDgwMTUwNjUwMWY5MmE2ZDFlZDMwZWM2YmIzZTZmOWY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544564693\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-646196690 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646196690\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2109991745 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFINkhma1FIZlUyMTdjTGR4N0liL1E9PSIsInZhbHVlIjoickhyaGF2ZGZOcU42UDFBN0tSSlpWd3RCTlREUGpKUXBCNjUrNUlFNzYvYitpSloxcHBQMXdNUVY4dmhMaFNheDBwZnAwazV5YytqOWQ5dmRqOVZhdE96bWlyQTZ6eUZtWEp2VjRBdmdxYThpZTJSNkxoaDU3c0xNTVFlcWxSREIiLCJtYWMiOiIzY2Y4MjI3MTAwYTgzZjIzMWYwMTE5OWIxNTVkZWJjNWFlMjdhOGIzZTZmNWJlM2IyYjQxMzVlMzhhZWQ5Y2Y3IiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkdRRHd3YXY5ZUlhMStVTENVS1U0M3c9PSIsInZhbHVlIjoib2xQZW5OQnBFeUpxcFFORVZ2ZDE1Sm5OcDVzNE5XOHhsWDJHdjNLcUo0amVDUWhIYThXdVhjS0JPU2UwWHBOTkFjWkVPV1VYNFZ2M1M1Z3ZyTnBjRGQyS2ZKSmpZWWl2L3l1Ym5oTlZNSEdsL20zUTUzVFZ2R2lCUmZPcE1YK00iLCJtYWMiOiJhN2EwNjVjN2QxYTQ3NmJkNTQzZTc5ZjAxNmNlOTdlZDdjMDJkNTk4MjcwYWRiY2ViOWZiYmZmNzk5M2ZhMTgxIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFINkhma1FIZlUyMTdjTGR4N0liL1E9PSIsInZhbHVlIjoickhyaGF2ZGZOcU42UDFBN0tSSlpWd3RCTlREUGpKUXBCNjUrNUlFNzYvYitpSloxcHBQMXdNUVY4dmhMaFNheDBwZnAwazV5YytqOWQ5dmRqOVZhdE96bWlyQTZ6eUZtWEp2VjRBdmdxYThpZTJSNkxoaDU3c0xNTVFlcWxSREIiLCJtYWMiOiIzY2Y4MjI3MTAwYTgzZjIzMWYwMTE5OWIxNTVkZWJjNWFlMjdhOGIzZTZmNWJlM2IyYjQxMzVlMzhhZWQ5Y2Y3IiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkdRRHd3YXY5ZUlhMStVTENVS1U0M3c9PSIsInZhbHVlIjoib2xQZW5OQnBFeUpxcFFORVZ2ZDE1Sm5OcDVzNE5XOHhsWDJHdjNLcUo0amVDUWhIYThXdVhjS0JPU2UwWHBOTkFjWkVPV1VYNFZ2M1M1Z3ZyTnBjRGQyS2ZKSmpZWWl2L3l1Ym5oTlZNSEdsL20zUTUzVFZ2R2lCUmZPcE1YK00iLCJtYWMiOiJhN2EwNjVjN2QxYTQ3NmJkNTQzZTc5ZjAxNmNlOTdlZDdjMDJkNTk4MjcwYWRiY2ViOWZiYmZmNzk5M2ZhMTgxIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109991745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-980597847 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980597847\", {\"maxDepth\":0})</script>\n"}}