<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="เมนูอาหาร - BBC HOME KITCHEN ร้านอาหารตามสั่งออนไลน์" />
    <meta name="author" content="BBC HOME KITCHEN" />
    <title>เมนูอาหาร - BBC HOME KITCHEN</title>
    <!-- Google Fonts - Prompt -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Bootstrap icons-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link href="{{ asset('css/styles.css') }}" rel="stylesheet" />
    <!-- Restaurant Background CSS -->
    <link href="{{ asset('css/restaurant-background.css') }}" rel="stylesheet" />
    <!-- Performance Optimizations CSS -->
    <link href="{{ asset('css/performance-optimizations.css') }}" rel="stylesheet" />
    <!-- Prompt Font CSS -->
    <link href="{{ asset('css/prompt-font.css') }}" rel="stylesheet" />
    <style>
        /* Global Font Family - Prompt */
        * {
            font-family: 'Prompt', sans-serif !important;
        }

        body {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 400;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .navbar-brand {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        .nav-link {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .btn {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .card-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .section-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        /* Restaurant Theme Styles */

        .navbar {
            background: rgba(255, 255, 255, 0.98) !important;
            border-bottom: 1px solid rgba(255, 107, 107, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #ff6b6b !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
            padding: 8px 16px !important;
            border-radius: 20px;
            transition: all 0.3s ease;
            margin: 0 4px;
        }

        .nav-link:hover {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .nav-link.active {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .navbar-toggler {
            border: 2px solid #ff6b6b;
            color: #ff6b6b;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
        }

        .header-restaurant {
            background: linear-gradient(
                135deg,
                rgba(255, 107, 107, 0.95) 0%,
                rgba(255, 87, 87, 0.9) 30%,
                rgba(255, 154, 0, 0.85) 70%,
                rgba(255, 193, 7, 0.8) 100%
            );
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 32px rgba(255, 107, 107, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .header-restaurant::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('../images/bbc-restaurant-bg.jpg');
            background-size: cover;
            background-position: center;
            opacity: 0.3;
            z-index: -1;
        }

        .section-title {
            color: #2d3436;
            font-weight: 700;
            margin-bottom: 3rem;
            position: relative;
            text-align: center;
            font-size: 2.8rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 2px;
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
        }

        .menu-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 250, 250, 0.95)) !important;
            border: 1px solid rgba(255, 107, 107, 0.15);
            border-radius: 16px;
            overflow: hidden;
            box-shadow:
                0 4px 20px rgba(255, 107, 107, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
            pointer-events: auto !important;
            position: relative;
            z-index: 1;
        }

        .menu-card:hover {
            background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(255, 248, 248, 0.98)) !important;
            transform: translateY(-5px) scale(1.01);
            box-shadow:
                0 12px 35px rgba(255, 107, 107, 0.18),
                0 4px 15px rgba(255, 87, 87, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 107, 107, 0.25);
        }

        .menu-card .card-img-top {
            transition: transform 0.3s ease, filter 0.3s ease;
            height: 200px;
            object-fit: cover;
            border-radius: 16px 16px 0 0;
            position: relative;
        }

        .menu-card:hover .card-img-top {
            transform: scale(1.05);
            filter: brightness(1.1) contrast(1.05);
        }

        .menu-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(
                180deg,
                rgba(255, 107, 107, 0.1) 0%,
                transparent 50%,
                rgba(0, 0, 0, 0.05) 100%
            );
            border-radius: 16px 16px 0 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .menu-card:hover::before {
            opacity: 1;
        }

        .menu-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow:
                0 2px 8px rgba(255, 107, 107, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 2;
            backdrop-filter: blur(10px);
        }

        .price-badge {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 700;
            box-shadow:
                0 2px 8px rgba(0, 184, 148, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 2;
            backdrop-filter: blur(10px);
        }

        .btn-order {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 50%, #ee5a24 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 28px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            box-shadow:
                0 4px 15px rgba(255, 107, 107, 0.3),
                0 2px 5px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            pointer-events: auto !important;
            position: relative;
            z-index: 10;
            overflow: hidden;
        }

        .btn-order:hover {
            background: linear-gradient(135deg, #ff5252 0%, #ff4444 50%, #e74c3c 100%);
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 6px 20px rgba(255, 107, 107, 0.4),
                0 3px 8px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-order::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-order:hover::before {
            left: 100%;
        }

        /* Card Body Enhancement */
        .card-body {
            padding: 1.5rem;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
            position: relative;
        }

        .card-title {
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 1rem;
            line-height: 1.4;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .card-text {
            color: #636e72;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        /* Card Footer Enhancement */
        .card-footer {
            background: linear-gradient(145deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
            border-top: 1px solid rgba(255, 107, 107, 0.1);
            padding: 1rem 1.5rem;
        }

        /* Fix Click Issues */
        .card, .menu-card, .btn, button, a {
            pointer-events: auto !important;
            position: relative;
            z-index: 1;
        }

        .card-footer, .card-body {
            pointer-events: auto !important;
            position: relative;
            z-index: 2;
        }

        /* Remove any blocking overlays */
        *::before, *::after {
            pointer-events: none !important;
        }

        .header-restaurant::before {
            pointer-events: none !important;
            z-index: -1 !important;
        }

        /* Beautiful Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        /* Loading Shimmer Effect */
        .loading-shimmer {
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0.1) 25%,
                rgba(255, 255, 255, 0.3) 50%,
                rgba(255, 255, 255, 0.1) 75%
            );
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        .footer-restaurant {
            background: linear-gradient(
                135deg,
                rgba(52, 58, 64, 0.95) 0%,
                rgba(73, 80, 87, 0.9) 100%
            ) !important;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Section Backgrounds */
        section {
            position: relative;
        }

        section.py-5 {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.97), rgba(248, 249, 250, 0.95));
            margin: 20px 0;
            border-radius: 20px;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.05),
                0 1px 3px rgba(0, 0, 0, 0.03),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }

        /* Text Enhancements */
        .text-white {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-title {
            background: linear-gradient(135deg, #ff6b6b, #ff9a00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            text-shadow: none;
        }

        .menu-meta {
            color: #636e72;
            font-size: 0.9rem;
        }

        .menu-meta i {
            color: #ff6b6b;
        }

        /* Empty state styles */
        .empty-state {
            padding: 3rem;
            text-align: center;
        }

        .empty-state .fs-1 {
            opacity: 0.5;
        }

        .category-badge {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation-->
    <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container px-4 px-lg-5">
                <a class="navbar-brand" href="{{route('index2')}}">🍽️ BBC HOME KITCHEN</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0 ms-lg-4">
                        <li class="nav-item"><a class="nav-link" href="{{route('index2')}}">หน้าแรก</a></li>
                        <li class="nav-item"><a class="nav-link" href="/event_new">ข่าวประชาสัมพันธ์</a></li>
                        <li class="nav-item"><a class="nav-link" href="{{ route('reviews.index') }}">รีวิว</a></li>
                        <li class="nav-item"><a class="nav-link active" aria-current="page" href="{{ route('menu.cards') }}">เมนูอาหาร</a></li>
                        <li class="nav-item"><a class="nav-link" href="/about">เกี่ยวกับเรา</a></li>
                    </ul>

                    <!-- Login/Register buttons on the right -->
                    <div class="d-flex">
                    @auth
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>{{ Auth::user()->name }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('customer.profile') }}">
                                    <i class="bi bi-person me-2"></i>โปรไฟล์ของฉัน
                                </a></li>
                                @if(Auth::user()->isAdmin())
                                    <li><a class="dropdown-item" href="{{ route('dashboard') }}">
                                        <i class="bi bi-speedometer2 me-2"></i>แดชบอร์ด
                                    </a></li>
                                @endif
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="bi bi-box-arrow-right me-2"></i>ออกจากระบบ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @else
                        <a href="{{route('login')}}" class="btn btn-outline-light me-2">
                            <i class="bi bi-box-arrow-in-right me-1"></i>เข้าสู่ระบบ
                        </a>
                        <a href="{{route('register')}}" class="btn btn-light">
                            <i class="bi bi-person-plus me-1"></i>สมัครสมาชิก
                        </a>
                    @endauth
                </div>



                </div>
            </div>

        </nav>
    <!-- Header-->
    <header class="header-restaurant py-5 text-white text-center">
        <div class="container px-4 px-lg-5">
            <h1 class="display-4 fw-bolder">🍽️ เมนูอาหาร</h1>
            <p class="lead fw-normal text-white-50">เลือกเมนูอาหารอร่อยจาก BBC HOME KITCHEN</p>
            <p class="text-white-50">มีเมนูทั้งหมด {{ $countProducts }} รายการ | อาหารสดใหม่ ส่งถึงบ้าน</p>
        </div>
    </header>

    <!-- Section-->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="text-center mb-5">
                <h2 class="section-title">🍽️ เมนูอาหารแนะนำ</h2>
                <p class="lead fw-normal text-muted mb-0">เลือกเมนูอาหารอร่อยจากร้านของเรา</p>
            </div>
            <div class="row gx-4 gx-lg-5 row-cols-1 row-cols-md-2 row-cols-xl-3 justify-content-center">
                @forelse($products as $product)
                    <div class="col mb-5">
                        <div class="card menu-card h-100">
                            <!-- Menu badge -->
                            <div class="menu-badge position-absolute" style="top: 1rem; left: 1rem; z-index: 10;">
                                🍽️ เมนูใหม่
                            </div>

                            <!-- Price badge -->
                            <div class="price-badge position-absolute" style="top: 1rem; right: 1rem; z-index: 10;">
                                ฿{{ number_format($product->price, 0) }}
                            </div>

                            <!-- Product image -->
                            @if($product->picture && $product->picture !== 'nopic.png' && file_exists(public_path('storage/products/' . $product->picture)))
                                <img class="card-img-top" src="{{ asset('storage/products/' . $product->picture) }}" alt="{{ $product->proname }}" />
                            @else
                                <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 200px; background: linear-gradient(135deg, #ffeaa7, #fdcb6e); font-size: 4rem;">
                                    🍽️
                                </div>
                            @endif

                            <!-- Product details -->
                            <div class="card-body p-4">
                                <div class="text-center">
                                    <!-- Category badge -->
                                    @if($product->category)
                                        <span class="category-badge">{{ $product->category->name }}</span>
                                    @endif

                                    <!-- Product title -->
                                    <h5 class="fw-bolder text-dark mb-3">{{ $product->proname }}</h5>
                                    <!-- Product description -->
                                    <p class="text-muted mb-3">{{ Str::limit($product->prodetail, 100) }}</p>

                                    <!-- Product meta info -->
                                    <div class="menu-meta mb-2">
                                        <div class="d-flex justify-content-center align-items-center mb-2">
                                            <i class="bi bi-tag me-2"></i>
                                            <span>ราคา: {{ number_format($product->price, 0) }} บาท</span>
                                        </div>
                                        @if($product->brand)
                                        <div class="d-flex justify-content-center align-items-center">
                                            <i class="bi bi-award me-2"></i>
                                            <span>แบรนด์: {{ $product->brand->brand_name }}</span>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <!-- Product actions -->
                            <div class="card-footer p-4 pt-0 border-top-0 bg-transparent">
                                <div class="text-center">
                                    <a class="btn btn-primary" href="{{ route('shop.show', $product->id) }}">
                                                            <i class="bi bi-eye me-1"></i>ดูรายละเอียด
                                                        </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal for product details -->
                    <div class="modal fade" id="productModal{{ $product->id }}" tabindex="-1" aria-labelledby="productModalLabel{{ $product->id }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="border-radius: 15px; border: none; background: white;">
                                <div class="modal-header" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; border-radius: 15px 15px 0 0;">
                                    <h5 class="modal-title fw-bold" id="productModalLabel{{ $product->id }}">🍽️ {{ $product->proname }}</h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body p-4" style="background: white; color: #333;">
                                    @if($product->picture && $product->picture !== 'nopic.png' && file_exists(public_path('storage/products/' . $product->picture)))
                                        <img src="{{ asset('storage/products/' . $product->picture) }}" alt="{{ $product->proname }}" class="img-fluid mb-4 rounded" style="border-radius: 10px; max-height: 300px; width: 100%; object-fit: cover;">
                                    @endif

                                    <div class="menu-meta mb-4" style="background: #f8f9fa; padding: 15px; border-radius: 10px; border: 1px solid #e9ecef;">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="bi bi-tag me-2 text-success"></i>
                                            <strong style="color: #333;">ราคา:</strong>
                                            <span class="ms-2 fw-bold text-success" style="font-size: 1.2rem;">{{ number_format($product->price, 0) }} บาท</span>
                                        </div>
                                        @if($product->category)
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="bi bi-list me-2 text-primary"></i>
                                            <strong style="color: #333;">หมวดหมู่:</strong>
                                            <span class="ms-2" style="color: #666;">{{ $product->category->name }}</span>
                                        </div>
                                        @endif
                                        @if($product->brand)
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-award me-2 text-warning"></i>
                                            <strong style="color: #333;">แบรนด์:</strong>
                                            <span class="ms-2" style="color: #666;">{{ $product->brand->brand_name }}</span>
                                        </div>
                                        @endif
                                    </div>

                                    <div class="content-section">
                                        <h6 class="fw-bold mb-3" style="color: #ff6b6b; border-bottom: 2px solid #ff6b6b; padding-bottom: 8px;">
                                            📝 รายละเอียดเมนู
                                        </h6>
                                        <div class="description-content" style="background: #fff; padding: 20px; border-radius: 10px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                            <p style="line-height: 1.8; color: #333; font-size: 16px; margin: 0; white-space: pre-wrap; font-weight: 400;">{{ $product->prodetail }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer" style="background: #f8f9fa; border-radius: 0 0 15px 15px; border-top: 1px solid #e9ecef;">
                                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border-radius: 25px; padding: 10px 25px; color: #666; border-color: #ddd;">
                                        <i class="bi bi-x-circle me-2"></i>ปิด
                                    </button>
                                    <button type="button" class="btn btn-primary" style="border-radius: 25px; padding: 10px 25px; background: linear-gradient(135deg, #ff6b6b, #ee5a24); border: none;">
                                        <i class="bi bi-cart-plus me-2"></i>เพิ่มลงตะกร้า
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="card menu-card p-5">
                            <div class="fs-1 mb-3">🍽️</div>
                            <h4 class="text-muted">ยังไม่มีเมนูอาหาร</h4>
                            <p class="text-muted">กรุณาติดตามเมนูใหม่จากเราในภายหลัง</p>
                            <a href="{{ route('index2') }}" class="btn btn-order">
                                <i class="bi bi-arrow-left me-2"></i>กลับไปหน้าแรก
                            </a>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($products->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $products->links() }}
                </div>
            @endif
        </div>
    </section>

    <!-- Footer-->
    <footer class="py-4 footer-restaurant text-white text-center">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-white mb-3">🍽️ BBC HOME KITCHEN</h5>
                    <p class="text-white-50">ร้านอาหารตามสั่งออนไลน์ อาหารอร่อยส่งถึงบ้าน</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-white-50 mb-0">&copy; 2024 BBC HOME KITCHEN</p>
                    <p class="text-white-50">สั่งอาหารอร่อยได้ทุกวัน</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap core JS-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Core theme JS-->
    <script src="{{ asset('js/scripts.js') }}"></script>

    <!-- Custom JS for Menu Page -->
    <script>
        // Enhanced card interactions with beautiful animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.menu-card');

            // Beautiful staggered fade-in animation
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.classList.add('loading-shimmer');

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.classList.remove('loading-shimmer');
                    card.classList.add('fade-in-up');
                }, index * 150);
            });

            // Enhanced hover effects with smooth transitions
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.01)';
                    this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add parallax effect to title
            const title = document.querySelector('.section-title');
            if (title) {
                window.addEventListener('scroll', () => {
                    const scrolled = window.pageYOffset;
                    const rate = scrolled * -0.5;
                    title.style.transform = `translateY(${rate}px)`;
                });
            }
        });
    </script>

    <!-- Restaurant Effects JS-->
    <script src="{{ asset('js/restaurant-effects.js') }}"></script>
</body>
</html>
