<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="@yield('description', 'BBC HOME KITCHEN - ร้านอาหารตามสั่งออนไลน์')" />
    <meta name="author" content="BBC HOME KITCHEN" />
    <title>@yield('title', 'BBC HOME KITCHEN')</title>
    <!-- Favicon-->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico" />
    <!-- Bootstrap icons-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css" rel="stylesheet" />
    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet" />
    <!-- Core theme CSS (includes Bootstrap)-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Restaurant Background CSS -->
    <link href="{{ asset('css/restaurant-background.css') }}" rel="stylesheet" />
    <!-- Image Placeholders CSS -->
    <link href="{{ asset('css/image-placeholders.css') }}" rel="stylesheet" />
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Prompt', sans-serif !important;
        }

        .navbar-brand {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        .nav-link {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .btn {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 500;
        }

        .card-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 600;
        }

        .section-title {
            font-family: 'Prompt', sans-serif !important;
            font-weight: 700;
        }

        /* Restaurant Theme Styles */
        .navbar {
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(8px);
            border-bottom: 2px solid rgba(255, 107, 107, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            position: relative;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #ff6b6b !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
            padding: 8px 16px !important;
            border-radius: 20px;
            transition: all 0.3s ease;
            margin: 0 4px;
        }

        .nav-link:hover {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .nav-link.active {
            color: white !important;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .navbar-toggler {
            border: 2px solid #ff6b6b;
            color: #ff6b6b;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
        }

        .footer-restaurant {
            background: linear-gradient(135deg, #2d3436, #636e72);
        }

        @yield('styles')
    </style>
    @stack('styles')
</head>
<body>
    <!-- Navigation-->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container px-4 px-lg-5">
            <a class="navbar-brand" href="{{route('index2')}}">🍽️ BBC HOME KITCHEN</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0 ms-lg-4">
                    <li class="nav-item"><a class="nav-link @yield('nav-home')" href="{{route('index2')}}">หน้าแรก</a></li>
                    <li class="nav-item"><a class="nav-link @yield('nav-news')" href="/event_new">ข่าวประชาสัมพันธ์</a></li>
                    <li class="nav-item"><a class="nav-link @yield('nav-reviews')" href="{{ route('reviews.index') }}">รีวิว</a></li>
                    <li class="nav-item"><a class="nav-link @yield('nav-shop')" href="{{ route('menu.cards') }}">เมนูอาหาร</a></li>
                    <li class="nav-item"><a class="nav-link @yield('nav-about')" href="/about">เกี่ยวกับเรา</a></li>
                </ul>

                <!-- Login/Register buttons on the right -->
                <div class="d-flex">
                @auth
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>{{ Auth::user()->name }}
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('customer.profile') }}">
                                <i class="bi bi-person me-2"></i>โปรไฟล์ของฉัน
                            </a></li>
                            @if(Auth::user()->isAdmin())
                                <li><a class="dropdown-item" href="{{ route('dashboard') }}">
                                    <i class="bi bi-speedometer2 me-2"></i>แดชบอร์ด
                                </a></li>
                            @endif
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right me-2"></i>ออกจากระบบ
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                @else
                    <a href="{{route('login')}}" class="btn btn-outline-light me-2">
                        <i class="bi bi-box-arrow-in-right me-1"></i>เข้าสู่ระบบ
                    </a>
                    <a href="{{route('register')}}" class="btn btn-light">
                        <i class="bi bi-person-plus me-1"></i>สมัครสมาชิก
                    </a>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    @yield('content')

    <!-- Footer-->
    <footer class="py-5 footer-restaurant">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-white mb-3">🍽️ BBC HOME KITCHEN</h5>
                    <p class="text-white-50">ร้านอาหารตามสั่งออนไลน์ อาหารอร่อยส่งถึงบ้าน ปรุงสดใหม่ทุกวัน</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-white-50 mb-0">&copy; 2024 BBC HOME KITCHEN</p>
                    <p class="text-white-50">ร้านอาหารตามสั่งออนไลน์</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap core JS-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Remix Icons -->
    <script src="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.js"></script>
    <!-- Image Handler JS -->
    <script src="{{ asset('js/image-handler.js') }}"></script>
    @stack('scripts')
</body>
</html>
