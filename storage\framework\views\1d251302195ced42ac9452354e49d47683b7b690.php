<?php $__env->startSection('content'); ?>

<div class="page-content">
    <div class="container-fluid">

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <h1 class="mb-2 card-title">จัดการแบรนด์สินค้า</h1>

                        <?php if(session('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo e(session('success')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <a href="<?php echo e(route('brand.create')); ?>" class="mb-3 btn btn-primary btn-rounded waves-effect waves-light">
                            <i class="ri-add-line"></i> เพิ่มแบรนด์ใหม่
                        </a>

                        <h5 class="mb-3">แบรนด์สินค้าทั้งหมดมีจำนวน <?php echo e($countBrand); ?> รายการ</h5>
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>ลำดับ</th>
                                        <th>ชื่อ</th>
                                        <th>คำอธิบาย</th>
                                        <th>ภาพ</th>
                                        <th>แก้ไข</th>
                                        <th>ลบ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item => $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <tr class="table-light">
                                        <th scope="row"><?php echo e($cat->id); ?></th>
                                        <td><?php echo e($cat->brand_name); ?></td>
                                        <td><?php echo e($cat->brand_slug); ?></td>
                                        <td>
                                            <?php if($cat->brand_image && file_exists(public_path('storage/brands/' . $cat->brand_image))): ?>
                                                <img src="<?php echo e(asset('storage/brands/'.$cat->brand_image)); ?>" alt="<?php echo e($cat->brand_name); ?>" style="width: 80px; height: 60px; object-fit: cover; border-radius: 8px;">
                                            <?php else: ?>
                                                <div class="text-center text-white rounded" style="width: 80px; height: 60px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #3cb371, #90ee90);">
                                                    <i class="ri-award-line" style="font-size: 1.5rem;"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('brand.edit', $cat->id)); ?>" class="btn btn-primary btn-sm waves-effect waves-light">
                                                <i class="ri-edit-line"></i> แก้ไข
                                            </a>
                                        </td>

                                        <td>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    title="ลบแบรนด์"
                                                    onclick="showDeleteModal(
                                                        'คุณต้องการลบแบรนด์นี้ใช่หรือไม่?',
                                                        'ลบแล้ว คุณจะไม่สามารถกู้คืนข้อมูลแบรนด์นี้ได้',
                                                        'แบรนด์: <?php echo e($cat->brand_name); ?>',
                                                        '<?php echo e(route('brand.destroy', $cat->id)); ?>'
                                                    )">
                                                <i class="ri-delete-bin-line"></i> ลบ
                                            </button>
                                        </td>
                                    </tr>

                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </tbody>
                            </table>
                            <!-- ลิงก์แบ่งหน้า -->
                            <?php echo e($brands->links()); ?>


                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>

<!-- Include Delete Confirmation Modal -->
<?php echo $__env->make('components.delete-confirmation-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.admin_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shopping67\resources\views/admin/brands/all_brabds.blade.php ENDPATH**/ ?>