{"__meta": {"id": "X968987ed6460c65237a244226e55172b", "datetime": "2025-10-06 09:30:34", "utime": **********.590534, "method": "GET", "uri": "/menu-cards", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.409997, "end": **********.590551, "duration": 0.18055391311645508, "duration_str": "181ms", "measures": [{"label": "Booting", "start": **********.409997, "relative_start": 0, "end": **********.506315, "relative_end": **********.506315, "duration": 0.09631800651550293, "duration_str": "96.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.506324, "relative_start": 0.09632706642150879, "end": **********.590553, "relative_end": 2.1457672119140625e-06, "duration": 0.0842289924621582, "duration_str": "84.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21378504, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "menu_cards", "param_count": null, "params": [], "start": **********.575756, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/menu_cards.blade.phpmenu_cards", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fmenu_cards.blade.php&line=1", "ajax": false, "filename": "menu_cards.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.584971, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}]}, "route": {"uri": "GET menu-cards", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Shopcontroller@menuCards", "namespace": null, "prefix": "", "where": [], "as": "menu.cards", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=74\" onclick=\"\">app/Http/Controllers/Shopcontroller.php:74-84</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02725, "accumulated_duration_str": "27.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 77}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.525946, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:77", "source": "app/Http/Controllers/Shopcontroller.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=77", "ajax": false, "filename": "Shopcontroller.php", "line": "77"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5334182, "duration": 0.02609, "duration_str": "26.09ms", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 0, "width_percent": 95.743}, {"sql": "select * from `products` limit 12 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.56138, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 95.743, "width_percent": 1.284}, {"sql": "select * from `categories` where `categories`.`id` in (1, 18, 24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5656319, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 97.028, "width_percent": 0.881}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.567598, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 97.908, "width_percent": 0.55}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.569529, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:81", "source": "app/Http/Controllers/Shopcontroller.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=81", "ajax": false, "filename": "Shopcontroller.php", "line": "81"}, "connection": "shopping67", "start_percent": 98.459, "width_percent": 0.44}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "menu_cards", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/menu_cards.blade.php", "line": 489}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.5802891, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 98.899, "width_percent": 1.101}]}, "models": {"data": {"App\\Models\\Product": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 17, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/menu-cards\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/menu-cards", "status_code": "<pre class=sf-dump id=sf-dump-220597791 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-220597791\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-848773769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-848773769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1712038019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1712038019\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkIrL2JWRk5McSswS3ZBcXZIWCtZRkE9PSIsInZhbHVlIjoiYVJ3QlVwbjE5K1pHdGx3aHo3cGhZRlFuRkFIdzQ0VjQ4aWg4eFZhNmhvcDd2elN2bzh3SkZjUTU4dkVyM0ZSbmZLZEk5V1J0UWR4Z3EyUnpvU2xFcUZMcU9tK0trZjdpeXFXSnR5ZXJHRXJVbGJybmtORWdjY1MrZ3ZnMTZ5VzFNSFpQelBrUHF1bjAyUWVYOFZtT0F1ZVlEV2ZGalZZMlg3QzBoYW9zZ3VtaHVINjNPWldsaHhMRmxSMVZVTFFxMnViZ21HN3NnQmtTY0hBaHgvVVZPRkdxcnBaa1V2UEJYTTlZaCs0WkNIRT0iLCJtYWMiOiI0YmUzYWQxMTFhNmNmZDhiNzYzZWU3OThiOGEzYzg5OTcwYjY5NTQ1NDk1MDg4NzQ1ZTcyM2RmNzg3OTcyMjNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpIUlpqVHVuV0FxbEJFZDNCYVFXcXc9PSIsInZhbHVlIjoiQWFwMzhjWHg4TCtpMmU4dnFNZHg2NU8wVEU2QTAzM3JVSWRZcG9nbll6eWlwVGFaREVOWWY5aWJEc05jaVNMRDI0U28xcmd1MUd5d2N3cVh5VysyRG9ockpHQTk2Q1Jrall5WlBDWW9JRFlUTW5HYVArcTdCcU1mNGZmYittekYiLCJtYWMiOiI0YTJhZjdiMTdjZmE1ODZjNWJkYWUyMGYyMTY0OTVjZGIyNWZhOGZjMjY3ZTFmYjUyNDhiZDlhMDAyYjkwMDU2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlU5a1R3aHlpaENmaVp5SnRpd1MzdlE9PSIsInZhbHVlIjoiV3RQczgrYmp5OVQrcEE3V2x5TjBJMHVTSlAxemtZU0s2a1orSXkzcGdCSE9ieFU2dWNub3hUUkRWRm9FTEkzQk1nNzcyNlZwSWpFd2t5TU0xNkhLSklDUXBEUjJHSUlRUTFLV0JHL2dYMDhiT2RNbkswQ1EvVXZNSXpXTnpuaXUiLCJtYWMiOiI1MjQxMjE3Yjc4MGQ2OGQxZGZlMTllYmU5NmI4OTNmNjU5MjViNGU4Y2IxMGZlNmMxZjk1OGE3N2U3NGQzNjZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|ciypaVlPs0XDfjrsxUZeJB2fnF13npcExqL2nhIjRV0odLHCNXgBMZHt59fU|$2y$10$S2aE4.mXyM2O6ZKRI.5aYO97Lu/xdKeg8EJ14VSNvvDZZ5WlGy8L.</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">38pgvJE1UDpAbpUVDhfLKYbtQu600dmF5Zqu0iOG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-822868676 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 06 Oct 2025 02:30:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlCNVRLTFdWb3M2RGplK2JtMkw1NUE9PSIsInZhbHVlIjoiTElKZzhSVXU2VjVXY2JsQ2VRc2VZeUVFMm13WjlZVXExRUw1YWpSTkp6M24vbURWdFVOM2lPZWFDQk44Mmw5dXhRb1JjV3UvQjJrL0U3SkxQWmZmODByVVRyRnhzZ1hiNzJKbTRJQ3YycDY4blIwSzlLZFV5L1F1M1Y3YVQyYU4iLCJtYWMiOiI0M2RjY2E5ZDE3M2IwN2ExZDg5YmU5YjZjZjMwYTc5MmQwZGUwZjMzZGE0YWExNjFkNmUwYmZkZmU0OGEwOTc4IiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:30:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjFPSnQwMkZmdXBtRWZTeDZsSlNqTEE9PSIsInZhbHVlIjoiTFlZUlQ0aXg0aE9aQmtUMjN4aFdFSGU5bW5VWENnRHBwK1FDaWIxYkYvc1pyMklWKzc1dTBhaklMejlQTDZBTjZKL1c0TmR4ZkwwTzRnQ1ova0NGRmtvSHBlNGcyTHBDRkM4cXlOZzN6Wkt3QXJ6NmxieHg3dFo2QmZrWWMzbm4iLCJtYWMiOiJkMTc5YzA2OTY0NTNjNWIzNDgyYmRmYzkwNTI4MjRkODQyZGEwMzdiMDA3OTk2MzJkYjdmYWNhMzJmMzlhZTdhIiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:30:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlCNVRLTFdWb3M2RGplK2JtMkw1NUE9PSIsInZhbHVlIjoiTElKZzhSVXU2VjVXY2JsQ2VRc2VZeUVFMm13WjlZVXExRUw1YWpSTkp6M24vbURWdFVOM2lPZWFDQk44Mmw5dXhRb1JjV3UvQjJrL0U3SkxQWmZmODByVVRyRnhzZ1hiNzJKbTRJQ3YycDY4blIwSzlLZFV5L1F1M1Y3YVQyYU4iLCJtYWMiOiI0M2RjY2E5ZDE3M2IwN2ExZDg5YmU5YjZjZjMwYTc5MmQwZGUwZjMzZGE0YWExNjFkNmUwYmZkZmU0OGEwOTc4IiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:30:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjFPSnQwMkZmdXBtRWZTeDZsSlNqTEE9PSIsInZhbHVlIjoiTFlZUlQ0aXg0aE9aQmtUMjN4aFdFSGU5bW5VWENnRHBwK1FDaWIxYkYvc1pyMklWKzc1dTBhaklMejlQTDZBTjZKL1c0TmR4ZkwwTzRnQ1ova0NGRmtvSHBlNGcyTHBDRkM4cXlOZzN6Wkt3QXJ6NmxieHg3dFo2QmZrWWMzbm4iLCJtYWMiOiJkMTc5YzA2OTY0NTNjNWIzNDgyYmRmYzkwNTI4MjRkODQyZGEwMzdiMDA3OTk2MzJkYjdmYWNhMzJmMzlhZTdhIiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:30:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822868676\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1556546083 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/menu-cards</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556546083\", {\"maxDepth\":0})</script>\n"}}