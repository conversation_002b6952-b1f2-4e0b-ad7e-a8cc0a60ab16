{"__meta": {"id": "Xc38227c4b9e97ad21385fb1f50f125b5", "datetime": "2025-10-06 09:36:47", "utime": **********.042815, "method": "GET", "uri": "/shop/product/7", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.794334, "end": **********.042835, "duration": 0.24850106239318848, "duration_str": "249ms", "measures": [{"label": "Booting", "start": **********.794334, "relative_start": 0, "end": **********.88793, "relative_end": **********.88793, "duration": 0.09359598159790039, "duration_str": "93.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.887941, "relative_start": 0.09360694885253906, "end": **********.042837, "relative_end": 1.9073486328125e-06, "duration": 0.15489602088928223, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21102696, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "product-detail", "param_count": null, "params": [], "start": **********.932579, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/product-detail.blade.phpproduct-detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fproduct-detail.blade.php&line=1", "ajax": false, "filename": "product-detail.blade.php", "line": "?"}}]}, "route": {"uri": "GET shop/product/{id}", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Shopcontroller@show", "namespace": null, "prefix": "", "where": [], "as": "shop.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=53\" onclick=\"\">app/Http/Controllers/Shopcontroller.php:53-72</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00258, "accumulated_duration_str": "2.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 57}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.907095, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:57", "source": "app/Http/Controllers/Shopcontroller.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=57", "ajax": false, "filename": "Shopcontroller.php", "line": "57"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `products` where `products`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9144192, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:57", "source": "app/Http/Controllers/Shopcontroller.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=57", "ajax": false, "filename": "Shopcontroller.php", "line": "57"}, "connection": "shopping67", "start_percent": 0, "width_percent": 51.55}, {"sql": "select * from `categories` where `categories`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.919018, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:57", "source": "app/Http/Controllers/Shopcontroller.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=57", "ajax": false, "filename": "Shopcontroller.php", "line": "57"}, "connection": "shopping67", "start_percent": 51.55, "width_percent": 8.14}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.920999, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:57", "source": "app/Http/Controllers/Shopcontroller.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=57", "ajax": false, "filename": "Shopcontroller.php", "line": "57"}, "connection": "shopping67", "start_percent": 59.69, "width_percent": 6.202}, {"sql": "select * from `products` where `category_id` = 1 and `id` != '7' limit 4", "type": "query", "params": [], "bindings": ["1", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 64}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.922673, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:64", "source": "app/Http/Controllers/Shopcontroller.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=64", "ajax": false, "filename": "Shopcontroller.php", "line": "64"}, "connection": "shopping67", "start_percent": 65.891, "width_percent": 8.915}, {"sql": "select * from `categories` where `categories`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.924587, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:64", "source": "app/Http/Controllers/Shopcontroller.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=64", "ajax": false, "filename": "Shopcontroller.php", "line": "64"}, "connection": "shopping67", "start_percent": 74.806, "width_percent": 5.426}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.926181, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:64", "source": "app/Http/Controllers/Shopcontroller.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=64", "ajax": false, "filename": "Shopcontroller.php", "line": "64"}, "connection": "shopping67", "start_percent": 80.233, "width_percent": 6.589}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "product-detail", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/product-detail.blade.php", "line": 281}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.031173, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 86.822, "width_percent": 13.178}]}, "models": {"data": {"App\\Models\\Product": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/shop/product/7\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/shop/product/7", "status_code": "<pre class=sf-dump id=sf-dump-2000658516 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2000658516\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1254351996 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1254351996\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1688390634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1688390634\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1643174131 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/menu-cards</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkIrL2JWRk5McSswS3ZBcXZIWCtZRkE9PSIsInZhbHVlIjoiYVJ3QlVwbjE5K1pHdGx3aHo3cGhZRlFuRkFIdzQ0VjQ4aWg4eFZhNmhvcDd2elN2bzh3SkZjUTU4dkVyM0ZSbmZLZEk5V1J0UWR4Z3EyUnpvU2xFcUZMcU9tK0trZjdpeXFXSnR5ZXJHRXJVbGJybmtORWdjY1MrZ3ZnMTZ5VzFNSFpQelBrUHF1bjAyUWVYOFZtT0F1ZVlEV2ZGalZZMlg3QzBoYW9zZ3VtaHVINjNPWldsaHhMRmxSMVZVTFFxMnViZ21HN3NnQmtTY0hBaHgvVVZPRkdxcnBaa1V2UEJYTTlZaCs0WkNIRT0iLCJtYWMiOiI0YmUzYWQxMTFhNmNmZDhiNzYzZWU3OThiOGEzYzg5OTcwYjY5NTQ1NDk1MDg4NzQ1ZTcyM2RmNzg3OTcyMjNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImdIbXpSRkdmU2kxSkVYT2hIZE1pYnc9PSIsInZhbHVlIjoiWWJoeG5KeTJnQWRGb2QrR1VNWkRGRmVDTlRDYm5xYlhmbWxYZU5Gc2dSOFYzelROeFI3aXRYdlF5SUFoUGhPQVdsR0RKMzNjdWE5MW4zeDZRdGlGaGFscU1Fd2x2OVhEbmZlN2YrK3lydE1sNlE5dWFVSXh3VFZwVnp3bkloOEMiLCJtYWMiOiI3NzBiMGY2OWEyNzdmMThmOGNmYjdlNGIxYWExM2MyMTdiMTA1ZmQwNjk0NGMwM2MzZjZmMmRhNTQ4MWM4ZTZjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkJocGN1Z2t2bkFlVitUb00yU2ZCakE9PSIsInZhbHVlIjoiRWtxTnF4Y3lmeEJRRklVUmZaWHBsK3FhdEgwcGMwUTdDTDFBSXZTanc1ZVRKOHM0ZHVrYVQrc3lyQmdwS2ViRVU2N0tHTmlJS09KbkxISVBmeUEvVHduQ3lRWENIT3d5aTNnQ1A5bXhCalNWSWdQOUhnNEY0NTdJRzZ0N0pwWW0iLCJtYWMiOiIyZmU4ZWQ2N2UxMzIwYWQ3ZDVkNjQ3MmViMWExZTRlZjY0NmEzMGVmNmZmZjkwOThlN2ZmNThkOTBlMmRiZTNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643174131\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-312505414 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|ciypaVlPs0XDfjrsxUZeJB2fnF13npcExqL2nhIjRV0odLHCNXgBMZHt59fU|$2y$10$S2aE4.mXyM2O6ZKRI.5aYO97Lu/xdKeg8EJ14VSNvvDZZ5WlGy8L.</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">38pgvJE1UDpAbpUVDhfLKYbtQu600dmF5Zqu0iOG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312505414\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1134287534 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 06 Oct 2025 02:36:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZqY21ybnhnemxRVmFjNXd5ajkwbXc9PSIsInZhbHVlIjoid2ZRNzZ2QmQwRllBdHFyTUhNYUZpYkhYS3N6Tks3eWE0cWZYTlBxLzZ3dEE2WTRXVHg0SEFmOS9WRE5Sa1hiZlVnckcvMU1QUHRnZ3RkY3dvWUJ0dHA2K0VOZlFrVHhRZVRrNDlPbFRYRnRrc0hpZ0o3dENDZHJ4ekY5bGNSdEsiLCJtYWMiOiI1OGNiZTI5NDkxOWU4NzcwM2IwMTFmYTM1NmM3MDlhMjc5YmVhNzI1MzE0NjI2ODUwNmEyOTE2YWQ0M2E0NDY5IiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:36:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InZOVWxONWtJTG1hMGYxZG1CMHZPL3c9PSIsInZhbHVlIjoiOVkrVmZQUzNRU0NjN2NJZ3lyRnhrM1hwSkpLc3hhK1dUWHVKbURHekQwOVEzQ0tGa2oxbDZFeFczWEVXU3c5U1U4bkErdmFoU0pud2p1RHRzZnRaVExmcXlMSXZrNkdvd3dxNDZOLzUwQnFlL044K1RWaEFLOXUyQ3dNMHpXcloiLCJtYWMiOiI2NmNhNWQ0MGNjODJhZDFjMzdlZWE2ZmJkNWI3M2EyNDdkNjBlYTQ1MWM0ZTdkNzcwMzEzNmViNmE5MDg1ZmRmIiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:36:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZqY21ybnhnemxRVmFjNXd5ajkwbXc9PSIsInZhbHVlIjoid2ZRNzZ2QmQwRllBdHFyTUhNYUZpYkhYS3N6Tks3eWE0cWZYTlBxLzZ3dEE2WTRXVHg0SEFmOS9WRE5Sa1hiZlVnckcvMU1QUHRnZ3RkY3dvWUJ0dHA2K0VOZlFrVHhRZVRrNDlPbFRYRnRrc0hpZ0o3dENDZHJ4ekY5bGNSdEsiLCJtYWMiOiI1OGNiZTI5NDkxOWU4NzcwM2IwMTFmYTM1NmM3MDlhMjc5YmVhNzI1MzE0NjI2ODUwNmEyOTE2YWQ0M2E0NDY5IiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:36:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InZOVWxONWtJTG1hMGYxZG1CMHZPL3c9PSIsInZhbHVlIjoiOVkrVmZQUzNRU0NjN2NJZ3lyRnhrM1hwSkpLc3hhK1dUWHVKbURHekQwOVEzQ0tGa2oxbDZFeFczWEVXU3c5U1U4bkErdmFoU0pud2p1RHRzZnRaVExmcXlMSXZrNkdvd3dxNDZOLzUwQnFlL044K1RWaEFLOXUyQ3dNMHpXcloiLCJtYWMiOiI2NmNhNWQ0MGNjODJhZDFjMzdlZWE2ZmJkNWI3M2EyNDdkNjBlYTQ1MWM0ZTdkNzcwMzEzNmViNmE5MDg1ZmRmIiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:36:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134287534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1226536813 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/shop/product/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226536813\", {\"maxDepth\":0})</script>\n"}}