{"__meta": {"id": "Xbb3b6ce14723d975be973aee455e204b", "datetime": "2025-09-25 19:49:19", "utime": **********.218763, "method": "GET", "uri": "/about", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758804558.937031, "end": **********.2188, "duration": 0.2817690372467041, "duration_str": "282ms", "measures": [{"label": "Booting", "start": 1758804558.937031, "relative_start": 0, "end": **********.072538, "relative_end": **********.072538, "duration": 0.13550686836242676, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.07255, "relative_start": 0.13551902770996094, "end": **********.21881, "relative_end": 1.0013580322265625e-05, "duration": 0.14626002311706543, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 18755928, "peak_usage_str": "18MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "about", "param_count": null, "params": [], "start": **********.140441, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/about.blade.phpabout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fabout.blade.php&line=1", "ajax": false, "filename": "about.blade.php", "line": "?"}}]}, "route": {"uri": "GET about", "middleware": "web", "uses": "Closure() {#240\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#235 …}\n  file: \"C:\\xampp\\htdocs\\shopping67\\routes\\web.php\"\n  line: \"35 to 37\"\n}", "namespace": null, "prefix": "", "where": [], "as": "about55", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Froutes%2Fweb.php&line=35\" onclick=\"\">routes/web.php:35-37</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/about\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/about", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-638253678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-638253678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-130226663 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-130226663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-119394885 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/shop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktjQmEvc3p5TGpJa1M3UmVMOHViRWc9PSIsInZhbHVlIjoiS2s5cjJOTlU0bXA2NXROelBXcEllM3owTFE4Uy9yU25kTjVic2dNM1ozQ2YvZE90ci9Cc0VmVWN6UTI1b3p1MFI4V1B4bEZHc0pwQXZKOHB6cExTMm5CVC9hT0FXU3AxeDlWTU9pRzlzZGFrbU9nMmd4aU5XWjVDREU2ZGRnZGkiLCJtYWMiOiI0NWU4NmFjMmM5NjcwYTU5MDkzNjdhNjA5MTkxMjYyZjJhNjcwMTY4Zjc2NTNhMGM1NDkwMzRkZTQ0Y2Q0MTZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZYSGNCWi81Y08zcEdlVWJwc255VFE9PSIsInZhbHVlIjoidXgvdU1CZzFCdzFoSEVHSUd4RUF1Rithc0xnM1dTZEtWL1V0cDBiTUR2ZmNqQnlxU3Z6aDAvb2g4cW0rOG5TOFIzeWxtZ1pJUWZjcHM5K1B2S3BrdFEyeFdKcHlOeWlsdlJqZ0RNNUcyaFpnMUR1Z2h1eFFrQU8yVXl1eFE2c2EiLCJtYWMiOiJiYmNmOTI0Zjc5NmZmYjI5OWEwZGY0YTBmMDdjMWQ2M2JlYTBhMTllZTFkY2RhZmNhYjA2M2MyNDNhYzA4MzFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119394885\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1755407567 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755407567\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-845214632 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkMyZ3hGR0hCcEk3c1MxcmV3eWdITkE9PSIsInZhbHVlIjoibkgybEx1ZndJUDlSYnZUVnJTMTV5V3NUanRtSm43NnNkUzVFLzRWaHl3THdONTdUdVJQM1RmMHBtQ1pXTlZmclhBWHF4aVB6Z2ZUaGVmbUFrOElMOTRTcW0rZ25VTG9Xb2NFRVJIdXJXamJZa1FyRGdmeGlseUVZTGJYYmgyRk4iLCJtYWMiOiI3ZDQ0YzNlOGFhMjMxOWVmNzY0NmViYWJjYmUwNzA2OWRlNGIzMWRlZGRkZDNjNDY4MmUyOTFjZTVmNGRjOWJiIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im9OeGRBZm9uSWszRUtqKzhndWFOOHc9PSIsInZhbHVlIjoiKzVteFpNSUQxamM1QnZEQ1RBNmxVZ1cyM3kyUVVaT3BDL3BHRm1NUW9kTDVlWlpGTHRRSTUvekYycE9xbGtkaG16RENsdnlqRWhKZnlIWThIR29meVBwN1BWNjNPSSs2RFVUM0dVRGQyVkNzeU9HQjliTlBQY3VtVmxDNHpabHYiLCJtYWMiOiI5MTNjZjdiYTQzZGYzZTllYTUzZjIzMWRlZGNmNGNkZmM4ZWY4YmMwOTcyMjA4NGViNWNhNWU0MjlkZDNkNDdiIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkMyZ3hGR0hCcEk3c1MxcmV3eWdITkE9PSIsInZhbHVlIjoibkgybEx1ZndJUDlSYnZUVnJTMTV5V3NUanRtSm43NnNkUzVFLzRWaHl3THdONTdUdVJQM1RmMHBtQ1pXTlZmclhBWHF4aVB6Z2ZUaGVmbUFrOElMOTRTcW0rZ25VTG9Xb2NFRVJIdXJXamJZa1FyRGdmeGlseUVZTGJYYmgyRk4iLCJtYWMiOiI3ZDQ0YzNlOGFhMjMxOWVmNzY0NmViYWJjYmUwNzA2OWRlNGIzMWRlZGRkZDNjNDY4MmUyOTFjZTVmNGRjOWJiIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im9OeGRBZm9uSWszRUtqKzhndWFOOHc9PSIsInZhbHVlIjoiKzVteFpNSUQxamM1QnZEQ1RBNmxVZ1cyM3kyUVVaT3BDL3BHRm1NUW9kTDVlWlpGTHRRSTUvekYycE9xbGtkaG16RENsdnlqRWhKZnlIWThIR29meVBwN1BWNjNPSSs2RFVUM0dVRGQyVkNzeU9HQjliTlBQY3VtVmxDNHpabHYiLCJtYWMiOiI5MTNjZjdiYTQzZGYzZTllYTUzZjIzMWRlZGNmNGNkZmM4ZWY4YmMwOTcyMjA4NGViNWNhNWU0MjlkZDNkNDdiIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845214632\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1080107288 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/about</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080107288\", {\"maxDepth\":0})</script>\n"}}