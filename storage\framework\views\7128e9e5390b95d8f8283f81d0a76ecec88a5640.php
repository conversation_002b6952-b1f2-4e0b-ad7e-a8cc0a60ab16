<?php $__env->startSection('content'); ?>
<!-- CSRF Token for JavaScript -->
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>"
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0 font-size-18">📰 จัดการข่าวประชาสัมพันธ์</h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">แดชบอร์ด</a></li>
                            <li class="breadcrumb-item active">ข่าวประชาสัมพันธ์</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="card-title mb-0">📢 รายการข่าวประชาสัมพันธ์ทั้งหมด</h4>
                            <a href="<?php echo e(route('event.create')); ?>" class="btn btn-success">
                                <i class="ri-add-circle-line me-2"></i>เพิ่มข่าวใหม่
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if(session('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="ri-check-circle-line me-2"></i><?php echo e(session('success')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if(session('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="ri-error-warning-line me-2"></i><?php echo e(session('error')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-primary">📊 สรุปข้อมูล</h5>
                                    <p class="text-muted mb-0">จำนวนข่าวทั้งหมด: <strong><?php echo e($countEvent); ?></strong> รายการ</p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">อัปเดตล่าสุด: <?php echo e(now()->format('d/m/Y H:i:s')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>ลำดับ</th>
                                        <th>หัวข้อข่าว</th>
                                        <th>รายละเอียดข่าว</th>
                                        <th>วันที่เผยแพร่</th>
                                        <th>ภาพ</th>
                                        <th>แก้ไข</th>
                                        <th>ลบ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $eventNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item => $ev): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="table-light">
                                        <th scope="row">
                                            <span class="badge bg-primary"><?php echo e($ev->id); ?></span>
                                        </th>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-1"><?php echo e($ev->title); ?></h6>
                                                    <small class="text-muted">
                                                        สร้างเมื่อ: <?php echo e($ev->created_at ? $ev->created_at->format('d/m/Y H:i') : '-'); ?>

                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="max-width: 300px;">
                                                <?php echo e(Str::limit($ev->description, 100)); ?>

                                                <?php if(strlen($ev->description) > 100): ?>
                                                    <br><small class="text-muted">... และอีก <?php echo e(strlen($ev->description) - 100); ?> ตัวอักษร</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo e($ev->event_date ? \Carbon\Carbon::parse($ev->event_date)->format('d/m/Y') : '-'); ?>

                                            </span>
                                            <?php if($ev->event_date): ?>
                                                <br><small class="text-muted">
                                                    <?php echo e(\Carbon\Carbon::parse($ev->event_date)->diffForHumans()); ?>

                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($ev->pic && file_exists(public_path('storage/event_news/' . $ev->pic))): ?>
                                                <img src="<?php echo e(asset('storage/event_news/'.$ev->pic)); ?>" alt="<?php echo e($ev->title); ?>" style="width: 100px; height: 70px; object-fit: cover; border-radius: 8px;">
                                            <?php else: ?>
                                                <div class="text-center text-white rounded" style="width: 100px; height: 70px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #ff6b6b, #ffa500);">
                                                    <i class="ri-news-line" style="font-size: 1.5rem;"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>

                                        <td>
                                            <a href="<?php echo e(route('event.edit', $ev->id)); ?>"
                                               class="btn btn-warning btn-sm waves-effect waves-light"
                                               title="แก้ไขข่าว">
                                                <i class="ri-edit-line"></i> แก้ไข
                                            </a>
                                        </td>

                                        <td>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    title="ลบข่าว"
                                                    onclick="showDeleteModal(
                                                        'คุณต้องการลบข่าวประชาสัมพันธ์ใช่หรือไม่?',
                                                        'ลบแล้ว คุณจะไม่สามารถกู้คืนข้อมูลข่าวนี้ได้',
                                                        '<?php echo e($ev->title); ?>',
                                                        '<?php echo e(route('event.delete', ['id' => $ev->id])); ?>'
                                                    )">
                                                <i class="ri-delete-bin-line"></i> ลบ
                                            </button>
                                        </td>
                                    </tr>

                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ri-newspaper-line" style="font-size: 3rem;"></i>
                                                <h5 class="mt-2">ยังไม่มีข่าวประชาสัมพันธ์</h5>
                                                <p>เริ่มต้นสร้างข่าวประชาสัมพันธ์แรกของคุณ</p>
                                                <a href="<?php echo e(route('event.create')); ?>" class="btn btn-primary">
                                                    <i class="ri-add-circle-line me-2"></i>เพิ่มข่าวใหม่
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if($eventNews->hasPages()): ?>
                            <div class="row mt-3">
                                <div class="col-sm-12 col-md-5">
                                    <div class="dataTables_info">
                                        แสดง <?php echo e($eventNews->firstItem()); ?> ถึง <?php echo e($eventNews->lastItem()); ?>

                                        จากทั้งหมด <?php echo e($eventNews->total()); ?> รายการ
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-7">
                                    <div class="dataTables_paginate paging_simple_numbers float-end">
                                        <?php echo e($eventNews->links()); ?>

                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Include Delete Confirmation Modal -->
<?php echo $__env->make('components.delete-confirmation-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.admin_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shopping67\resources\views/admin/event_news/event_news.blade.php ENDPATH**/ ?>