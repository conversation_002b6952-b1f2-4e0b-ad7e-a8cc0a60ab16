{"__meta": {"id": "X7520fc9a64abaab5ee07aa126c6743bd", "datetime": "2025-09-25 20:12:49", "utime": **********.606022, "method": "GET", "uri": "/menu-cards", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.002638, "end": **********.606047, "duration": 0.6034088134765625, "duration_str": "603ms", "measures": [{"label": "Booting", "start": **********.002638, "relative_start": 0, "end": **********.238286, "relative_end": **********.238286, "duration": 0.23564791679382324, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238305, "relative_start": 0.23566699028015137, "end": **********.60605, "relative_end": 3.0994415283203125e-06, "duration": 0.36774492263793945, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21386496, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "menu_cards", "param_count": null, "params": [], "start": **********.344413, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/menu_cards.blade.phpmenu_cards", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fmenu_cards.blade.php&line=1", "ajax": false, "filename": "menu_cards.blade.php", "line": "?"}}]}, "route": {"uri": "GET menu-cards", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Shopcontroller@menuCards", "namespace": null, "prefix": "", "where": [], "as": "menu.cards", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=74\" onclick=\"\">app/Http/Controllers/Shopcontroller.php:74-84</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0153899***********, "accumulated_duration_str": "15.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 77}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.285339, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:77", "source": "app/Http/Controllers/Shopcontroller.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=77", "ajax": false, "filename": "Shopcontroller.php", "line": "77"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.300113, "duration": 0.01279, "duration_str": "12.79ms", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 0, "width_percent": 83.106}, {"sql": "select * from `products` limit 12 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.315522, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 83.106, "width_percent": 3.574}, {"sql": "select * from `categories` where `categories`.`id` in (1, 2, 3, 4, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.323368, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 86.68, "width_percent": 3.314}, {"sql": "select * from `brands` where `brands`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 78}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.328241, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:78", "source": "app/Http/Controllers/Shopcontroller.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=78", "ajax": false, "filename": "Shopcontroller.php", "line": "78"}, "connection": "shopping67", "start_percent": 89.994, "width_percent": 4.288}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Shopcontroller.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Shopcontroller.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.332261, "duration": 0.000359999***********, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Shopcontroller.php:81", "source": "app/Http/Controllers/Shopcontroller.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FShopcontroller.php&line=81", "ajax": false, "filename": "Shopcontroller.php", "line": "81"}, "connection": "shopping67", "start_percent": 94.282, "width_percent": 2.339}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "menu_cards", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/menu_cards.blade.php", "line": 489}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.587089, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 96.621, "width_percent": 3.379}]}, "models": {"data": {"App\\Models\\Product": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 16, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/menu-cards\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/menu-cards", "status_code": "<pre class=sf-dump id=sf-dump-1968269581 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1968269581\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-116188422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-116188422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-335677706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-335677706\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-373844376 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBqOHdhL3U2dlpJN0dvQmtXVHY2V2c9PSIsInZhbHVlIjoiQWJrVW1ZSUIwc1pici9QMUlTRXdBUlZPRStsWS9EQkV6ZzR4NkI0NExILzQzOFpHcEh5ZGhmd0hTSWJCYVZTb0dQNnd5Yi9NTVZDUmJyZmpQazREbEhHYkk2K1pXN1MzNjN3RWNpTHNMV3lsL1hveXJsL0hhWjhLSnVqR2oycWciLCJtYWMiOiIxMWI1OWZkMTBiZjRiNzBhYjQxMjUzOGIxYWJlM2UzMDhiMDNkZDJiYjNkZGRkZmU3NjAwN2Y2ZmE4ZWMyOWIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImRZVkdNVWNEZVdQL2VPWWwxRVY5MUE9PSIsInZhbHVlIjoiVHlPaHpINXFTZkk4V0hhSk4wLzRsR3FleGs0Z2xDMVZDZDRKRERaUUJ4bmo3aE9JVzB0bWdSejgwazEyVGxPSi91dUZQb3ZQcUJBOXZvVXJDSmFqbjVHZmxVUUNJQ2JGQmFVR3BaWUdiK2cvbGFqRG1Vd3RiQ1BuTmE3a3JGcy8iLCJtYWMiOiIwMjY4MjI2MGQyYzM2Y2M4YmYzNzk1NDViMGYyNDYwZmQ0MWZhZWM3NjNmYTA2OThjOTVmNjBiYzg1YmEyZmQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373844376\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1476668181 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WMTIWfNahNQIltL7Sh8nxhpUhpWn05qNcCODNhVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476668181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2142575734 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 13:12:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InZMMnlsYndKRTdyMHFFRkk4d2Fkdnc9PSIsInZhbHVlIjoiN1RVZXRTYUpRUU9CSkVCeDc1dmY5czkwb3M0Q0d6eEo5TkZ3endOeVZQU2J6aEMyclZ2T0wwWE9KY0lYUTBXdkw3TFRNbXk3OXA2WUp3M1cxUXl2OWxyWnRaTVBCcjRmL0RzUGdKS0UvbXQwbzhGWGpvQ3N6TWVQdkJ2alMxV0UiLCJtYWMiOiJiNzI4ZTA3YmMxMDRlYTlhZjQzOTczNzg0NTQ5MTRiNGExOWUyYTM4ZDIyN2EwMzc0YzQ1ZjAxYzE4YjRjOGRiIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:12:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Imx4VURJVFRWbyszMStpRWZvZFhsaUE9PSIsInZhbHVlIjoid0dSUnAra3Z3d29Nd2pscVpUdElsQy9GeEZHd1FqVUVSWk9SdmhPazB3VDgvdW5RTlRMS2pZV2JFY2V1Ykp6bUNLaUZNV0RqTXZTKzNkdm9oOTZ6eFhUYUhTWjIzRDB6MnF2V2pMK0xta2Q3VS9aT0cvVmtNWTYvdEl5UDRuZUkiLCJtYWMiOiJiZDYxMjBiNzc0NWY3NjA5ZDFhZjE4NTYyM2FlMTc0ZDkxMDRmMTBlZjFjNjI4MDAzZGJlMzNkZTBjOWIwMGYzIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:12:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InZMMnlsYndKRTdyMHFFRkk4d2Fkdnc9PSIsInZhbHVlIjoiN1RVZXRTYUpRUU9CSkVCeDc1dmY5czkwb3M0Q0d6eEo5TkZ3endOeVZQU2J6aEMyclZ2T0wwWE9KY0lYUTBXdkw3TFRNbXk3OXA2WUp3M1cxUXl2OWxyWnRaTVBCcjRmL0RzUGdKS0UvbXQwbzhGWGpvQ3N6TWVQdkJ2alMxV0UiLCJtYWMiOiJiNzI4ZTA3YmMxMDRlYTlhZjQzOTczNzg0NTQ5MTRiNGExOWUyYTM4ZDIyN2EwMzc0YzQ1ZjAxYzE4YjRjOGRiIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:12:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Imx4VURJVFRWbyszMStpRWZvZFhsaUE9PSIsInZhbHVlIjoid0dSUnAra3Z3d29Nd2pscVpUdElsQy9GeEZHd1FqVUVSWk9SdmhPazB3VDgvdW5RTlRMS2pZV2JFY2V1Ykp6bUNLaUZNV0RqTXZTKzNkdm9oOTZ6eFhUYUhTWjIzRDB6MnF2V2pMK0xta2Q3VS9aT0cvVmtNWTYvdEl5UDRuZUkiLCJtYWMiOiJiZDYxMjBiNzc0NWY3NjA5ZDFhZjE4NTYyM2FlMTc0ZDkxMDRmMTBlZjFjNjI4MDAzZGJlMzNkZTBjOWIwMGYzIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:12:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142575734\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1735476328 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/menu-cards</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735476328\", {\"maxDepth\":0})</script>\n"}}