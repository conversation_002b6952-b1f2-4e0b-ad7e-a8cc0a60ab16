<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\EventNewsController;
use App\Http\Controllers\IndexController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\Admin\AdminReviewController;
use App\Http\Controllers\Shopcontroller;
use App\Models\EventNews;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

/*Route::get('/', function () {
    return view('index');
})->name('index2');*/

Route::get('/123', function() {
    return view('index');
});

Route::get('/about', function() {
    return view('about');
}) ->name('about55');

Route::get('/regis', function() {
    return view('register1');
}) ->name('regis');

// Route::get('/shop', function() {
//     return view('shop');
// });

Route::get('/event_new', [EventNewsController::class, 'publicIndex'])->name('event_new.index');



Route::get('/dashboard', function () {
    return view('admin.admin_master');
})->middleware(['auth', 'verified', 'admin'])->name('dashboard');

Route::get('/dash', function () {
    return view('admin.dashboard');
})->middleware(['auth', 'verified', 'admin'])->name('dashboard1');

// Admin Dashboard Route
Route::get('/admin/dashboard', [App\Http\Controllers\AdminDashboardController::class, 'index'])
    ->middleware(['auth', 'verified', 'admin'])
    ->name('admin.dashboard');

Route::get('/u1', function () {
    return view('admin.user');
})->middleware(['auth', 'verified', 'admin'])->name('user1');

Route::middleware(['auth', 'admin'])->group(function () {
  /*  Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    use App\Http\Controllers\AdminController;*/

// หน้าแก้ไขข้อมูลโปรไฟล์
Route::get('/admin/profile/edit', [AdminController::class, 'editProfile'])->name('admin.profile.edit');

// การอัปเดตโปรไฟล์
Route::post('/admin/profile/update', [AdminController::class, 'updateProfile'])->name('admin.profile.update');

});


Route::middleware(['auth', 'admin'])->group(function(){
    Route::controller(CategoryController::class)->group(function(){
        Route::get('all/cate','index')->name('category.index');
        Route::delete('/category/{id}','delete')->name('category.delete');
        Route::get('/category/add', 'create')->name('category.create');
        Route::post('category/','store')->name('category.store');
        Route::get('/category/{category}/edit','edit')->name('category.edit');
        Route::put('/category/{category}','update')->name('category.update');
    });

    Route::controller(BrandController::class)->group(function(){
        Route::get('brand/all', 'index')->name('brand.index');
        Route::get('brand/create', 'create')->name('brand.create');
        Route::post('brand/store', 'store')->name('brand.store');
        Route::get('brand/{brand}/edit', 'edit')->name('brand.edit');
        Route::put('brand/{brand}', 'update')->name('brand.update');
        Route::delete('brand/{brand}', 'destroy')->name('brand.destroy');
    });

    Route::controller(ProductController::class)->group(function(){
        Route::get('products/all','index')->name('product.index');
        Route::get('product/add','create')->name('product.create');
        Route::post('product/store','store')->name('product.store');
        Route::delete('/product/delete/{id}','delete')->name('product.delete');
        Route::get('/products/{id}/edit','edit')->name('products.edit');
        Route::put('/products/{id}', 'update') ->name('products.update');
    });

    Route::controller(EventNewsController::class)->group(function(){
        Route::get('events/all','index')->name('event.index');
        Route::get('events/add','create')->name('event.create');
        Route::post('events/store','store')->name('event.store');
        Route::delete('/events/delete/{id}','delete')->name('event.delete');
        Route::get('/events/{eventNews}/edit','edit')->name('event.edit');
        Route::put('/events/{eventNews}','update')->name('event.update');
    });

    // Member Management Routes
    Route::controller(App\Http\Controllers\Admin\MemberController::class)->group(function(){
        Route::get('admin/members','index')->name('admin.members.index');
        Route::get('admin/members/{id}','show')->name('admin.members.show');
        Route::delete('admin/members/{id}','destroy')->name('admin.members.destroy');
        Route::post('admin/members/bulk-delete','bulkDelete')->name('admin.members.bulk-delete');
    });
});

Route::controller(IndexController::class)->group(function(){

    Route::get('/','index')->name('index2');
    Route::get('/categories/{id}','showCategoryProducts')->name('categories.show');

});

Route::controller(Shopcontroller::class)->group(function(){
    Route::get('/shop', 'shop')->name('shop.index');
    Route::get('/shop/category/{id}', 'showCategoryProducts')->name('shop.category');
    Route::get('/shop/product/{id}', 'show')->name('shop.show');
});

// Menu Cards Route
Route::get('/menu-cards', [Shopcontroller::class, 'menuCards'])->name('menu.cards');

// Review Routes (Public)
Route::controller(ReviewController::class)->group(function(){
    Route::get('/reviews', 'index')->name('reviews.index');
    Route::get('/reviews/create', 'create')->name('reviews.create');
    Route::get('/reviews/{id}', 'show')->name('reviews.show');
});

// Review Routes (Authenticated)
Route::middleware('auth')->group(function(){
    Route::post('/reviews', [ReviewController::class, 'store'])->name('reviews.store');
});

// Customer Profile Routes
Route::middleware('auth')->group(function(){
    Route::get('/profile', [App\Http\Controllers\CustomerProfileController::class, 'show'])->name('customer.profile');
    Route::put('/profile', [App\Http\Controllers\CustomerProfileController::class, 'update'])->name('customer.profile.update');
    Route::get('/profile/change-password', [App\Http\Controllers\CustomerProfileController::class, 'showChangePasswordForm'])->name('customer.change-password');
    Route::put('/profile/change-password', [App\Http\Controllers\CustomerProfileController::class, 'updatePassword'])->name('customer.update-password');
});

// Admin Review Routes (Protected)
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function(){
    Route::controller(AdminReviewController::class)->prefix('reviews')->name('reviews.')->group(function(){
        Route::get('/', 'index')->name('index');
        Route::get('/{id}', 'show')->name('show');
        Route::delete('/{id}', 'destroy')->name('destroy');
        Route::patch('/{id}/approve', 'approve')->name('approve');
        Route::patch('/{id}/reject', 'reject')->name('reject');
        Route::post('/bulk-approve', 'bulkApprove')->name('bulk-approve');
        Route::post('/bulk-reject', 'bulkReject')->name('bulk-reject');
    });
});

require __DIR__.'/auth.php';
