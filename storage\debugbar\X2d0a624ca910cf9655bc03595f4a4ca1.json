{"__meta": {"id": "X2d0a624ca910cf9655bc03595f4a4ca1", "datetime": "2025-09-25 19:49:22", "utime": **********.166709, "method": "GET", "uri": "/about", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758804561.899913, "end": **********.16673, "duration": 0.2668168544769287, "duration_str": "267ms", "measures": [{"label": "Booting", "start": 1758804561.899913, "relative_start": 0, "end": **********.111328, "relative_end": **********.111328, "duration": 0.21141481399536133, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.111364, "relative_start": 0.21145081520080566, "end": **********.166735, "relative_end": 5.0067901611328125e-06, "duration": 0.05537104606628418, "duration_str": "55.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 18755928, "peak_usage_str": "18MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "about", "param_count": null, "params": [], "start": **********.153111, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/about.blade.phpabout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fabout.blade.php&line=1", "ajax": false, "filename": "about.blade.php", "line": "?"}}]}, "route": {"uri": "GET about", "middleware": "web", "uses": "Closure() {#240\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#235 …}\n  file: \"C:\\xampp\\htdocs\\shopping67\\routes\\web.php\"\n  line: \"35 to 37\"\n}", "namespace": null, "prefix": "", "where": [], "as": "about55", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Froutes%2Fweb.php&line=35\" onclick=\"\">routes/web.php:35-37</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/about\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/about", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-618182607 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-618182607\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-791336548 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791336548\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkhENFVHTmd3Mm9tOTJBSnVtK3BuS1E9PSIsInZhbHVlIjoiRENLc1p6cXlCQXVnQlE5T3J5U3JscUlxV0tFUlU4ZzlBSHAzMjZVNVk3R0ppZ3FMYVRxcE9qWUd2U3JhUTNVaVVCcTRZS3ovUUQrcElEaldNSzFnaEJES2Q1cU8zbjJFMG9nTFk1bFBTVFU3d0lSWFlMZ1YwVzFUWnpiRFE1alciLCJtYWMiOiJlZjM0MzllYTM3MjAzNmUzYzQ0ZmMwZTBlM2E4NDVmNjZmYmQyOTRhYzVkMDg1YWRhM2MwMGNkMzRkZTdkNGJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImlpN3B2MGZ5TG03SXZLcS9RU3NpUHc9PSIsInZhbHVlIjoiNUVobXRGSGVTRmxKZzA2MGg0TkNQOFJ4RGdPbHQwc3RsY1JwRzA3Q1hQQ212dmQ2Skd4ZUxoRU4xazZ3bjNjbzdWZE9JZ3JCcExlbFBXQmdSMlhvSHYxTjZwZTF5aWxOaWE1eFVtSmRGYUN0ekpkS0llS0lObkxGdUd3Sm5FbVkiLCJtYWMiOiIwOWRmYWNjNTYxZWU5MWUyN2EyNmY1NzI3MDliMjk5NjdmMDQ1MzYyMzBiY2IzNTgxZGZhZWI2ZjA2NmU1YjJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-66528816 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HyDnBjmgK4IMao6eis1o3ntQRiFLtUnfxXuu2E7R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66528816\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-51673193 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 12:49:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IllFS2MzSzFqMTlvdmd1b2J0S3pJSlE9PSIsInZhbHVlIjoiMG5WVEI4RVlRc01LbnIyekZvaGFVT1g5UzBRRm5pcHhrUzdOV0w1NG5GeVRhd0c1cnVFQ3ZUSk9zN2tLOUtNS0lqV2RSK2l3eTU3bFFVSitBVyt6cDhLM0c0bHVYY1JWNmxYb3ZMY3oxVHlna2xkcm5CblZvL3hUZVpDWG5ZTjkiLCJtYWMiOiI0MjcxZjRjNTViNGMwYzc5NjI2MDFjODE2YWE2Nzg3NmFjYmM3MjA0MDc2M2NjMDU0MzYwMGY0ZDZjNGVhOGVjIiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImxMVlpwanlxTFBrWEs3MHVSVzlqd0E9PSIsInZhbHVlIjoibXN0WEZBTGx3R001VUFLSTBlYVZkMHdIYUkyc2d5RmluQW5MOTJnczVNUjFMNmNOOG9TTFJvNldKT3g2UHdXODdjYi9VVDRxU29VK0VSZml0bHIvYmUyRU9kTmVJKzRUM25aMldkMDNXNlVoMzlIdEZPaEtQRzBmd2RKWVhhcWMiLCJtYWMiOiI5ZTgwMDM2MjJlYjkwMDU4MmZiNzFlZmY4NTRiNDUyMDgwMTUwNjUwMWY5MmE2ZDFlZDMwZWM2YmIzZTZmOWY3IiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 14:49:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IllFS2MzSzFqMTlvdmd1b2J0S3pJSlE9PSIsInZhbHVlIjoiMG5WVEI4RVlRc01LbnIyekZvaGFVT1g5UzBRRm5pcHhrUzdOV0w1NG5GeVRhd0c1cnVFQ3ZUSk9zN2tLOUtNS0lqV2RSK2l3eTU3bFFVSitBVyt6cDhLM0c0bHVYY1JWNmxYb3ZMY3oxVHlna2xkcm5CblZvL3hUZVpDWG5ZTjkiLCJtYWMiOiI0MjcxZjRjNTViNGMwYzc5NjI2MDFjODE2YWE2Nzg3NmFjYmM3MjA0MDc2M2NjMDU0MzYwMGY0ZDZjNGVhOGVjIiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImxMVlpwanlxTFBrWEs3MHVSVzlqd0E9PSIsInZhbHVlIjoibXN0WEZBTGx3R001VUFLSTBlYVZkMHdIYUkyc2d5RmluQW5MOTJnczVNUjFMNmNOOG9TTFJvNldKT3g2UHdXODdjYi9VVDRxU29VK0VSZml0bHIvYmUyRU9kTmVJKzRUM25aMldkMDNXNlVoMzlIdEZPaEtQRzBmd2RKWVhhcWMiLCJtYWMiOiI5ZTgwMDM2MjJlYjkwMDU4MmZiNzFlZmY4NTRiNDUyMDgwMTUwNjUwMWY5MmE2ZDFlZDMwZWM2YmIzZTZmOWY3IiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 14:49:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51673193\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-153368109 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aLbaJOHquwxcaX08M314RUpfg1R6TauSHmU93mnd</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/about</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153368109\", {\"maxDepth\":0})</script>\n"}}