{"__meta": {"id": "Xa361a3888c8b072ae7bc4abc15868aeb", "datetime": "2025-09-25 20:29:16", "utime": 1758806956.011168, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.734053, "end": 1758806956.011187, "duration": 1.2771341800689697, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": **********.734053, "relative_start": 0, "end": **********.910974, "relative_end": **********.910974, "duration": 0.17692112922668457, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.910998, "relative_start": 0.17694520950317383, "end": 1758806956.01119, "relative_end": 2.86102294921875e-06, "duration": 1.1001918315887451, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 22030368, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "index", "param_count": null, "params": [], "start": **********.114731, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.phpindex", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\IndexController@index", "namespace": null, "prefix": "", "where": [], "as": "index2", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=22\" onclick=\"\">app/Http/Controllers/IndexController.php:22-56</a>"}, "queries": {"nb_statements": 38, "nb_failed_statements": 0, "accumulated_duration": 0.14944, "accumulated_duration_str": "149ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 8, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.953894, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "SHOW TABLES LIKE 'reviews'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9545, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping67", "start_percent": 0, "width_percent": 3.145}, {"sql": "SHOW COLUMNS FROM reviews", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 99}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 87}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.964637, "duration": 0.0453, "duration_str": "45.3ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:99", "source": "app/Http/Controllers/IndexController.php:99", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=99", "ajax": false, "filename": "IndexController.php", "line": "99"}, "connection": "shopping67", "start_percent": 3.145, "width_percent": 30.313}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.033884, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:29", "source": "app/Http/Controllers/IndexController.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=29", "ajax": false, "filename": "IndexController.php", "line": "29"}, "connection": "shopping67", "start_percent": 33.458, "width_percent": 0.455}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.041426, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:30", "source": "app/Http/Controllers/IndexController.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=30", "ajax": false, "filename": "IndexController.php", "line": "30"}, "connection": "shopping67", "start_percent": 33.913, "width_percent": 0.328}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0469, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:31", "source": "app/Http/Controllers/IndexController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=31", "ajax": false, "filename": "IndexController.php", "line": "31"}, "connection": "shopping67", "start_percent": 34.241, "width_percent": 0.488}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.052974, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping67", "start_percent": 34.73, "width_percent": 0.622}, {"sql": "select * from `products` where `products`.`category_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0626678, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping67", "start_percent": 35.352, "width_percent": 0.642}, {"sql": "select * from `event_news` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0694299, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:36", "source": "app/Http/Controllers/IndexController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=36", "ajax": false, "filename": "IndexController.php", "line": "36"}, "connection": "shopping67", "start_percent": 35.994, "width_percent": 0.488}, {"sql": "select * from `products` order by RAND() limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.073646, "duration": 0.015960000000000002, "duration_str": "15.96ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 36.483, "width_percent": 10.68}, {"sql": "select * from `categories` where `categories`.`id` in (1, 2, 3, 4, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0950801, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 47.163, "width_percent": 0.542}, {"sql": "select * from `brands` where `brands`.`id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.09968, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping67", "start_percent": 47.705, "width_percent": 0.388}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 491}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "start": **********.8387082, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 48.093, "width_percent": 0.422}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.849206, "duration": 0.01627, "duration_str": "16.27ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 48.514, "width_percent": 10.887}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 5 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["5", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.870311, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 59.402, "width_percent": 0.482}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8735719, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 59.884, "width_percent": 2.503}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 2 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["2", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.8801951, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 62.386, "width_percent": 0.341}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8838632, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 62.728, "width_percent": 2.65}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 7 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["7", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.890714, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 65.377, "width_percent": 0.375}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.894761, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 65.752, "width_percent": 2.884}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 6 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["6", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.902104, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 68.636, "width_percent": 0.462}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.905502, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 69.098, "width_percent": 3.466}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 4 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["4", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.913871, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 72.564, "width_percent": 0.555}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9177592, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 73.12, "width_percent": 2.784}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 3 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["3", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.924846, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 75.903, "width_percent": 0.401}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.928042, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 76.305, "width_percent": 2.683}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.935151, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 78.988, "width_percent": 0.442}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9385428, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 79.43, "width_percent": 2.857}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.9458349, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 82.287, "width_percent": 0.361}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.94932, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 82.649, "width_percent": 2.422}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.956619, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 85.071, "width_percent": 0.381}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.95969, "duration": 0.00484, "duration_str": "4.84ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 85.452, "width_percent": 3.239}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.968014, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 88.691, "width_percent": 0.495}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.971598, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 89.186, "width_percent": 3.319}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.979905, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 92.505, "width_percent": 0.375}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.982708, "duration": 0.0053, "duration_str": "5.3ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 92.88, "width_percent": 3.547}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 56}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 657}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.990834, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:56", "source": "app/Models/Product.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=56", "ajax": false, "filename": "Product.php", "line": "56"}, "connection": "shopping67", "start_percent": 96.427, "width_percent": 0.315}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping67' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping67", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 42}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 663}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.993477, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "Product.php:42", "source": "app/Models/Product.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=42", "ajax": false, "filename": "Product.php", "line": "42"}, "connection": "shopping67", "start_percent": 96.741, "width_percent": 2.824}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 1 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["1", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 64}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 663}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1758806956.0006611, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Product.php:64", "source": "app/Models/Product.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=64", "ajax": false, "filename": "Product.php", "line": "64"}, "connection": "shopping67", "start_percent": 99.565, "width_percent": 0.435}]}, "models": {"data": {"App\\Models\\Category": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Product": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Brand": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\EventNews": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FEventNews.php&line=1", "ajax": false, "filename": "EventNews.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 76, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-2080810517 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2080810517\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-338219349 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-338219349\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-906784911 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-906784911\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-715587467 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/products/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktiV0lGMVI1Nzg2VVV5MlMrZUlmb1E9PSIsInZhbHVlIjoiYldSL09ndVFTZ3BaSm4vYzV0YnRXRE9Md09Ub214bXVOSFF2Qi85Y2ZwUDVtODZsRW03d1VJanllV1RQUDhFUXRtUkhrQWtURWFMaVBhUjZUU3RxbUpSdkVISm9OQWZBbDR5aERmSWJWNURQMkFhTmhXRkQyblA3Q1lHZU1YcnMiLCJtYWMiOiJkMDAwYmM2NDJmNjAzZmE3MmJmOWRmNGUwN2IxMzdkOWNlMThmMjQzNmFlMDNkNDhkZDM4ZDkzNGNmMWU5NmM5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik4wV0F6UmZHQmdBTDIvS3NuUHUxd3c9PSIsInZhbHVlIjoiK3F3ejcyM09mMFB6YUJLMkFScWQ1by9ySGVaTElpaytRS2RpV1Bjb28xMUF0SllabEp3QWx3YVNYQitSd1ZTZUh3MEg4S3h4VkJQdFNpUU9jd3RHZ1VFakNubnExUG1yWHpBWWJvaS9HOGFwcE9NMWkyVjFFZXY5eGhYc0J6VjUiLCJtYWMiOiJhMDVkMzZkYzUxMjk5MzkyZDM0NDMwZDg0ZGJkNzk5Y2Q2NDM3MTk1MDAyZmVkYWIzOTA0OTBiYjEzZWQ0ZjRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715587467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1768130628 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WMTIWfNahNQIltL7Sh8nxhpUhpWn05qNcCODNhVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768130628\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2069366343 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 25 Sep 2025 13:29:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InozTTdYeUhqbEJVLzVwM2tFcmQ2M3c9PSIsInZhbHVlIjoidmoyVGR6V3pjajM3NW95VXF0VVR4b2xsSXZMY1Q5aFFwbU5aNkxKMXBtVDRMYnMwOEpyb3ozcXpDL3ozZm9sUWdOdHVVMlpEb3MwaEloVUxON1NqdlZ6UFB2V0ZKMXFqaDBYNXo2QWQvTzVpVXRiNU9waHg1WVA0NWR0SFdITU0iLCJtYWMiOiI5ZDBlOTk5MzU0ZjRlM2Q0OGM1ZjM2NzFkZjcwMWM2ZjgwMTgzOGYxMWRkNDY1OTc3ODhjMDQ3NTNiNzI1MTc3IiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:29:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkhaV0kxbXhWL25ISnc4OWczUm1vVmc9PSIsInZhbHVlIjoib1BxaUFQamlrL1NPT2I2YjB0UGdneU9yNnltUkVSMlFlN2czU2FoRVBEdHhUc0Jvc3RVenBJaFVuZHVJeDYvQXJBeXJGVll0WUJTamd2d2JUdk44Qm44M1FYY3JSYU1pamhpNXdtMHdBbERNZHNhME1uY0NRZ3dDQUpVQTgwNGwiLCJtYWMiOiJkMzk0ZjQ2NDQxOGZhZDk3NDhmOWQxNDhkNThkZDliNDZiMGQzODk3YzI5NzZiNzdiZGRiZDI5YzcxNzRiZmI4IiwidGFnIjoiIn0%3D; expires=Thu, 25 Sep 2025 15:29:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InozTTdYeUhqbEJVLzVwM2tFcmQ2M3c9PSIsInZhbHVlIjoidmoyVGR6V3pjajM3NW95VXF0VVR4b2xsSXZMY1Q5aFFwbU5aNkxKMXBtVDRMYnMwOEpyb3ozcXpDL3ozZm9sUWdOdHVVMlpEb3MwaEloVUxON1NqdlZ6UFB2V0ZKMXFqaDBYNXo2QWQvTzVpVXRiNU9waHg1WVA0NWR0SFdITU0iLCJtYWMiOiI5ZDBlOTk5MzU0ZjRlM2Q0OGM1ZjM2NzFkZjcwMWM2ZjgwMTgzOGYxMWRkNDY1OTc3ODhjMDQ3NTNiNzI1MTc3IiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:29:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkhaV0kxbXhWL25ISnc4OWczUm1vVmc9PSIsInZhbHVlIjoib1BxaUFQamlrL1NPT2I2YjB0UGdneU9yNnltUkVSMlFlN2czU2FoRVBEdHhUc0Jvc3RVenBJaFVuZHVJeDYvQXJBeXJGVll0WUJTamd2d2JUdk44Qm44M1FYY3JSYU1pamhpNXdtMHdBbERNZHNhME1uY0NRZ3dDQUpVQTgwNGwiLCJtYWMiOiJkMzk0ZjQ2NDQxOGZhZDk3NDhmOWQxNDhkNThkZDliNDZiMGQzODk3YzI5NzZiNzdiZGRiZDI5YzcxNzRiZmI4IiwidGFnIjoiIn0%3D; expires=Thu, 25-Sep-2025 15:29:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069366343\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1514219932 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LonticFINCT5mfIEMEwIQ2QJA4R16WNe8L2zfgGO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514219932\", {\"maxDepth\":0})</script>\n"}}