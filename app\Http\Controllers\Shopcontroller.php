<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Product;

use Illuminate\Support\Facades\Storage;
use Image;

class Shopcontroller extends Controller
{
    //
    public function shop()
    {
        // ดึงข้อมูลหมวดหมู่สินค้าทั้งหมด
        $categories = Category::all();

        // ดึงข้อมูลสินค้าทั้งหมดพร้อมความสัมพันธ์กับหมวดหมู่และแบรนด์
        $products = Product::with(['category', 'brand'])->get();

        // นับจำนวนสินค้าทั้งหมด
        $countPro = Product::count();

        return view('shop', compact('categories', 'products', 'countPro'));
    }
    public function showCategoryProducts($id)
    {
        try {
            // ดึงหมวดหมู่พร้อมสินค้าของหมวดหมู่นั้น
            $category = Category::with(['products.brand'])->findOrFail($id);

            // ดึงหมวดหมู่ทั้งหมดสำหรับ navigation
            $categories = Category::all();

            // ดึงสินค้าในหมวดหมู่นี้
            $products = Product::with(['category', 'brand'])
                              ->where('category_id', $id)
                              ->get();

            $countPro = $category->products->count();

            // ส่งข้อมูลไปยัง View
            return view('cateshow', compact('category', 'categories', 'products', 'countPro'));
        } catch (\Exception $e) {
            // ถ้าเกิด error ให้ redirect กลับไปหน้า shop
            return redirect()->route('shop.index')->with('error', 'ไม่พบหมวดหมู่ที่ต้องการ');
        }
    }

    public function show($id)
    {
        try {
            // ดึงข้อมูลสินค้าพร้อมความสัมพันธ์
            $product = Product::with(['category', 'brand'])->findOrFail($id);

            // ดึงสินค้าที่เกี่ยวข้อง (ในหมวดหมู่เดียวกัน)
            $relatedProducts = Product::with(['category', 'brand'])
                                    ->where('category_id', $product->category_id)
                                    ->where('id', '!=', $id)
                                    ->limit(4)
                                    ->get();

            // ส่งข้อมูลไปยัง View
            return view('product-detail', compact('product', 'relatedProducts'));
        } catch (\Exception $e) {
            // ถ้าเกิด error ให้ redirect กลับไปหน้า shop
            return redirect()->route('shop.index')->with('error', 'ไม่พบสินค้าที่ต้องการ');
        }
    }

    public function menuCards(Request $request)
    {
        // ดึงข้อมูลสินค้าทั้งหมดพร้อมความสัมพันธ์กับหมวดหมู่และแบรนด์
        $products = Product::with(['category', 'brand'])
                          ->paginate(12); // แสดง 12 รายการต่อหน้า

        // นับจำนวนสินค้าทั้งหมด
        $countProducts = Product::count();

        return view('menu_cards', compact('products', 'countProducts'));
    }
}
