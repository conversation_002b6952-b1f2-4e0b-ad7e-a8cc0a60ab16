{"__meta": {"id": "X108259308eb526543d91a1bd3e1e114c", "datetime": "2025-10-06 09:54:00", "utime": **********.383432, "method": "GET", "uri": "/admin/reviews", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.182256, "end": **********.383447, "duration": 0.20119094848632812, "duration_str": "201ms", "measures": [{"label": "Booting", "start": **********.182256, "relative_start": 0, "end": **********.293333, "relative_end": **********.293333, "duration": 0.11107707023620605, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.293342, "relative_start": 0.11108613014221191, "end": **********.383449, "relative_end": 2.1457672119140625e-06, "duration": 0.09010696411132812, "duration_str": "90.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21488696, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "admin.reviews.index", "param_count": null, "params": [], "start": **********.370133, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/reviews/index.blade.phpadmin.reviews.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Freviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.372686, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "components.delete-confirmation-modal", "param_count": null, "params": [], "start": **********.373165, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/components/delete-confirmation-modal.blade.phpcomponents.delete-confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fcomponents%2Fdelete-confirmation-modal.blade.php&line=1", "ajax": false, "filename": "delete-confirmation-modal.blade.php", "line": "?"}}, {"name": "admin.admin_master", "param_count": null, "params": [], "start": **********.373607, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/admin_master.blade.phpadmin.admin_master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fadmin_master.blade.php&line=1", "ajax": false, "filename": "admin_master.blade.php", "line": "?"}}, {"name": "sweetalert::alert", "param_count": null, "params": [], "start": **********.374208, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}}, {"name": "admin.body.header", "param_count": null, "params": [], "start": **********.374923, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.phpadmin.body.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "admin.body.slidemenu", "param_count": null, "params": [], "start": **********.377487, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/slidemenu.blade.phpadmin.body.slidemenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fslidemenu.blade.php&line=1", "ajax": false, "filename": "slidemenu.blade.php", "line": "?"}}, {"name": "admin.body.footer", "param_count": null, "params": [], "start": **********.377999, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/footer.blade.phpadmin.body.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/reviews", "middleware": "web, auth, verified, admin, Closure", "controller": "App\\Http\\Controllers\\Admin\\AdminReviewController@index", "as": "admin.reviews.index", "namespace": null, "prefix": "admin/reviews", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=19\" onclick=\"\">app/Http/Controllers/Admin/AdminReviewController.php:19-50</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.018109999999999998, "accumulated_duration_str": "18.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.317578, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping67", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.324572, "duration": 0.015960000000000002, "duration_str": "15.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping67", "start_percent": 0, "width_percent": 88.128}, {"sql": "select count(*) as aggregate from `reviews`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3465831, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:39", "source": "app/Http/Controllers/Admin/AdminReviewController.php:39", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=39", "ajax": false, "filename": "AdminReviewController.php", "line": "39"}, "connection": "shopping67", "start_percent": 88.128, "width_percent": 1.491}, {"sql": "select * from `reviews` order by `created_at` desc limit 15 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.348511, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:39", "source": "app/Http/Controllers/Admin/AdminReviewController.php:39", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=39", "ajax": false, "filename": "AdminReviewController.php", "line": "39"}, "connection": "shopping67", "start_percent": 89.619, "width_percent": 1.601}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.351934, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:39", "source": "app/Http/Controllers/Admin/AdminReviewController.php:39", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=39", "ajax": false, "filename": "AdminReviewController.php", "line": "39"}, "connection": "shopping67", "start_percent": 91.22, "width_percent": 1.325}, {"sql": "select * from `products` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.354055, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:39", "source": "app/Http/Controllers/Admin/AdminReviewController.php:39", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=39", "ajax": false, "filename": "AdminReviewController.php", "line": "39"}, "connection": "shopping67", "start_percent": 92.546, "width_percent": 0.994}, {"sql": "select * from `users` where `users`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.355649, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:39", "source": "app/Http/Controllers/Admin/AdminReviewController.php:39", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=39", "ajax": false, "filename": "AdminReviewController.php", "line": "39"}, "connection": "shopping67", "start_percent": 93.539, "width_percent": 1.049}, {"sql": "select count(*) as aggregate from `reviews`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.35773, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:43", "source": "app/Http/Controllers/Admin/AdminReviewController.php:43", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=43", "ajax": false, "filename": "AdminReviewController.php", "line": "43"}, "connection": "shopping67", "start_percent": 94.589, "width_percent": 0.883}, {"sql": "select count(*) as aggregate from `reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.359271, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:44", "source": "app/Http/Controllers/Admin/AdminReviewController.php:44", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=44", "ajax": false, "filename": "AdminReviewController.php", "line": "44"}, "connection": "shopping67", "start_percent": 95.472, "width_percent": 0.828}, {"sql": "select count(*) as aggregate from `reviews` where `status` = 'approved'", "type": "query", "params": [], "bindings": ["approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 45}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3609161, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:45", "source": "app/Http/Controllers/Admin/AdminReviewController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=45", "ajax": false, "filename": "AdminReviewController.php", "line": "45"}, "connection": "shopping67", "start_percent": 96.3, "width_percent": 0.607}, {"sql": "select count(*) as aggregate from `reviews` where `status` = 'rejected'", "type": "query", "params": [], "bindings": ["rejected"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AdminReviewController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Admin\\AdminReviewController.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.362482, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "AdminReviewController.php:46", "source": "app/Http/Controllers/Admin/AdminReviewController.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAdmin%2FAdminReviewController.php&line=46", "ajax": false, "filename": "AdminReviewController.php", "line": "46"}, "connection": "shopping67", "start_percent": 96.908, "width_percent": 0.607}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "admin.body.header", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.3753302, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "admin.body.header:32", "source": "view::admin.body.header:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=32", "ajax": false, "filename": "header.blade.php", "line": "32"}, "connection": "shopping67", "start_percent": 97.515, "width_percent": 2.485}]}, "models": {"data": {"App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Review": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FReview.php&line=1", "ajax": false, "filename": "Review.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/reviews\"\n]", "_flash": "array:2 [\n  \"old\" => array:15 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.background\"\n    4 => \"alert.config.width\"\n    5 => \"alert.config.heightAuto\"\n    6 => \"alert.config.padding\"\n    7 => \"alert.config.showConfirmButton\"\n    8 => \"alert.config.showCloseButton\"\n    9 => \"alert.config.confirmButtonText\"\n    10 => \"alert.config.cancelButtonText\"\n    11 => \"alert.config.timerProgressBar\"\n    12 => \"alert.config.customClass\"\n    13 => \"alert.config.icon\"\n    14 => \"alert.config\"\n  ]\n  \"new\" => []\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/reviews", "status_code": "<pre class=sf-dump id=sf-dump-1626960169 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1626960169\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-622279427 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-622279427\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1834449637 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1834449637\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1852357801 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/admin/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkIrL2JWRk5McSswS3ZBcXZIWCtZRkE9PSIsInZhbHVlIjoiYVJ3QlVwbjE5K1pHdGx3aHo3cGhZRlFuRkFIdzQ0VjQ4aWg4eFZhNmhvcDd2elN2bzh3SkZjUTU4dkVyM0ZSbmZLZEk5V1J0UWR4Z3EyUnpvU2xFcUZMcU9tK0trZjdpeXFXSnR5ZXJHRXJVbGJybmtORWdjY1MrZ3ZnMTZ5VzFNSFpQelBrUHF1bjAyUWVYOFZtT0F1ZVlEV2ZGalZZMlg3QzBoYW9zZ3VtaHVINjNPWldsaHhMRmxSMVZVTFFxMnViZ21HN3NnQmtTY0hBaHgvVVZPRkdxcnBaa1V2UEJYTTlZaCs0WkNIRT0iLCJtYWMiOiI0YmUzYWQxMTFhNmNmZDhiNzYzZWU3OThiOGEzYzg5OTcwYjY5NTQ1NDk1MDg4NzQ1ZTcyM2RmNzg3OTcyMjNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJHVldLN2pTTkRIN0V6dWN1WVNEOVE9PSIsInZhbHVlIjoiNmlwcGo3cThmcnBrcHhCamplQVFzV0I1alRuUUJzVFBDbVQ5aW50Q2JYb0Q4YUxBWmNReEZEK0t5YkhhRi81RHV2YWdnQ0xDc3lpckY3S2ZEWEI0a0UxNUtibDc2KzMzRjZqNStQby9LSDVZL0trYm5PdzVLYkw0OHRyZkVkWVAiLCJtYWMiOiJiMmRiOTlkNWI4ODhjNGI2MmU0N2IyZWFiMjE3MTJhOGM0MDIxN2U4NmZhN2NiM2I2MjlkY2ZhYThkYWYxNWY5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdJUUpKOVhsNlRQQnBrV3NuaC9OcGc9PSIsInZhbHVlIjoiLzR5NXA1aWQrSEIrY3pwL1ZpMHJkam15UWJ0cWpjakl2K0sramZWeXRQY3V4dzlGWEpUR2pkb2Rldlp1YXN3WlJPUUZreWFQdVFJQjNwWG8xYUVYamt6RUt3aERaUFZCZjUwbGVrWXFDZ3Z4TTBoak8zSThIeXhDNEVsNUpDMlEiLCJtYWMiOiJhYjVmOGYyMzBkZjkyNmI5NzAwMTM3NTE0Y2Q3OTQwZDQ5ODg1M2RiN2UzMGRlZjVhOGVjNjIxZTgzODM1NWVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852357801\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-17304709 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|ciypaVlPs0XDfjrsxUZeJB2fnF13npcExqL2nhIjRV0odLHCNXgBMZHt59fU|$2y$10$S2aE4.mXyM2O6ZKRI.5aYO97Lu/xdKeg8EJ14VSNvvDZZ5WlGy8L.</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">38pgvJE1UDpAbpUVDhfLKYbtQu600dmF5Zqu0iOG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17304709\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-959665610 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 06 Oct 2025 02:54:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlI4Q3c1RzRxRXp6ZzkzMXM3cEhzZWc9PSIsInZhbHVlIjoibXJweEd1WFN6T3F3OVlkT1k5Y09EZ3FyVzgrQ3RLWE5vV1QwWnhWdkxEYW85TnpkcHp0YUJiVThFUUwxSXVNcjRHaG9VSVIvOFU5WE80bWM0R1ZCYXpZMDVLdzd0eTI4N3lnelBQazBkWHNMeUtjYzV2a3gzYlNmM2ZQRmlQYUoiLCJtYWMiOiJhMGJmNDA3YTczYzgyM2Y5ZTdhYTNmNDQ4OTVlYWYwMTk0Nzk2MzI3OTYyMWFlNzlhOGI3MjAzMTJkN2U0ZjI2IiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:54:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ik0xUEtDbzRwc0laMXVXWHpRTy8yc1E9PSIsInZhbHVlIjoidWorcWRPeDlDNG5QZE40L3c2ZzV1aFRBZjFkdHEyNjZBRzV2Yy96eXAvV0RhRjAyVEFuOHJTbjhBOXh1enJGRVZsSHQ3SXFDRnduVnV3NmtidXhEbFpkRnFmWEwzRkRKWTFCQmMzbDNoWlEvTVA3NDFLTUVOTVVPM1hDUFNGL0siLCJtYWMiOiJmNmI1YTdkOTNlMDM1ZDkxMzM0YWU0YjJjZWEyMzM1OGMzYzcyYWU2ZDNiZDk4MGVjZDMzNTFhOTRmYjYxYzBjIiwidGFnIjoiIn0%3D; expires=Mon, 06 Oct 2025 04:54:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlI4Q3c1RzRxRXp6ZzkzMXM3cEhzZWc9PSIsInZhbHVlIjoibXJweEd1WFN6T3F3OVlkT1k5Y09EZ3FyVzgrQ3RLWE5vV1QwWnhWdkxEYW85TnpkcHp0YUJiVThFUUwxSXVNcjRHaG9VSVIvOFU5WE80bWM0R1ZCYXpZMDVLdzd0eTI4N3lnelBQazBkWHNMeUtjYzV2a3gzYlNmM2ZQRmlQYUoiLCJtYWMiOiJhMGJmNDA3YTczYzgyM2Y5ZTdhYTNmNDQ4OTVlYWYwMTk0Nzk2MzI3OTYyMWFlNzlhOGI3MjAzMTJkN2U0ZjI2IiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:54:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ik0xUEtDbzRwc0laMXVXWHpRTy8yc1E9PSIsInZhbHVlIjoidWorcWRPeDlDNG5QZE40L3c2ZzV1aFRBZjFkdHEyNjZBRzV2Yy96eXAvV0RhRjAyVEFuOHJTbjhBOXh1enJGRVZsSHQ3SXFDRnduVnV3NmtidXhEbFpkRnFmWEwzRkRKWTFCQmMzbDNoWlEvTVA3NDFLTUVOTVVPM1hDUFNGL0siLCJtYWMiOiJmNmI1YTdkOTNlMDM1ZDkxMzM0YWU0YjJjZWEyMzM1OGMzYzcyYWU2ZDNiZDk4MGVjZDMzNTFhOTRmYjYxYzBjIiwidGFnIjoiIn0%3D; expires=Mon, 06-Oct-2025 04:54:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959665610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-589661626 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rjtoi4EqyNUxEbZkTigzRPosuhd6v55EOkeNa2kC</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/admin/reviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"23 characters\">alert.config.background</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"23 characters\">alert.config.heightAuto</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.confirmButtonText</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.cancelButtonText</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589661626\", {\"maxDepth\":0})</script>\n"}}