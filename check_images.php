<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\EventNews;

try {
    echo "🔍 ตรวจสอบรูปภาพข่าวสาร...\n\n";
    
    $news = EventNews::orderBy('created_at', 'desc')->take(10)->get();
    
    if ($news->count() == 0) {
        echo "❌ ไม่พบข่าวสารในระบบ\n";
        exit;
    }
    
    echo "📰 ข่าวสารทั้งหมด ({$news->count()} รายการ):\n\n";
    
    foreach($news as $index => $item) {
        $num = $index + 1;
        echo "{$num}. 📝 {$item->title}\n";
        echo "   📅 วันที่: {$item->event_date}\n";
        
        if($item->pic) {
            echo "   🖼️ ไฟล์รูป: {$item->pic}\n";
            
            $storagePath = storage_path('app/public/event_news/' . $item->pic);
            $publicPath = public_path('storage/event_news/' . $item->pic);
            
            echo "   📁 Storage: " . (file_exists($storagePath) ? '✅ มี' : '❌ ไม่มี') . "\n";
            echo "   🌐 Public: " . (file_exists($publicPath) ? '✅ มี' : '❌ ไม่มี') . "\n";
            echo "   🔗 URL: " . asset('storage/event_news/' . $item->pic) . "\n";
            
            if (file_exists($publicPath)) {
                $size = filesize($publicPath);
                echo "   📏 ขนาด: " . number_format($size / 1024, 2) . " KB\n";
            }
        } else {
            echo "   🖼️ ไฟล์รูป: ❌ ไม่มี\n";
        }
        echo "\n";
    }
    
    // ตรวจสอบโฟลเดอร์
    echo "📂 ตรวจสอบโฟลเดอร์:\n";
    $storageDir = storage_path('app/public/event_news');
    $publicDir = public_path('storage/event_news');
    
    echo "  📁 Storage dir: " . (is_dir($storageDir) ? '✅ มี' : '❌ ไม่มี') . " - {$storageDir}\n";
    echo "  🌐 Public dir: " . (is_dir($publicDir) ? '✅ มี' : '❌ ไม่มี') . " - {$publicDir}\n";
    
    if (is_dir($storageDir)) {
        $files = scandir($storageDir);
        $imageFiles = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
        });
        echo "  📊 ไฟล์รูปใน storage: " . count($imageFiles) . " ไฟล์\n";
    }
    
    if (is_dir($publicDir)) {
        $files = scandir($publicDir);
        $imageFiles = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
        });
        echo "  📊 ไฟล์รูปใน public: " . count($imageFiles) . " ไฟล์\n";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
